<template>
	<view class="u-page">

		<view class="white-card">
			<view class="card-head">
				<view class="head-title">基础信息</view>
			</view>
			<!--card-head卡片标题-->
			<view class="card1">
				<view class="text-list table-list">
					<view class="clum">文件类型：</view>
					<view class="text">{{formatterDocClass(formData.docClass)}}</view>
				</view>
				<view class="text-list table-list">
					<view class="clum">文件名称：</view>
					<view class="text">{{formData.docName}}</view>
				</view>
				<view class="text-list table-list">
					<view class="clum">文件编号：</view>
					<view class="text">{{formData.docId}}</view>
				</view>
				<view class="text-list table-list">
					<view class="clum">文件版本：</view>
					<view class="text">{{formData.versionValue}}</view>
				</view>
				<view class="text-list table-list">
					<view class="clum">编制部门：</view>
					<view class="text">{{formData.deptName}}</view>
				</view>
				<view class="text-list table-list">
					<view class="clum">编制人：</view>
					<view class="text">{{formData.nickName}}</view>
				</view>
				<view class="text-list table-list">
					<view class="clum">编制时间：</view>
					<view class="text">{{parseTime(formData.applyTime,'{y}-{m}-{d} {h}:{i}')}}</view>
				</view>
				<view class="text-list table-list">
					<view class="clum">当前版本：</view>
					<u--text type="primary" @click="handlePreview(formData.preStandardDoc.fileId)" v-if="formData.preStandardDoc" :text="formData.preStandardDoc.fileName"></u--text>
				</view>
				<view class="text-list table-list">
					<view class="clum">版本附件：</view>
					<view class="text">
						<u--text type="primary" @click="handlePreview(item.fileId)"  v-for="(item, index) in formData.preAppendixes" :key='index' :text="item.fileName"></u--text>
					</view>
				</view>
				<view class="text-list table-list">
					<view class="clum">变更原因：</view>
					<view class="text">{{formData.changeReason}}</view>
				</view>
				<view class="text-list table-list">
					<view class="clum">变更内容：</view>
					<view class="text">{{formData.content}}</view>
				</view>
				<view class="text-list table-list">
					<view class="clum">培训记录：</view>
					<view class="text">
						<u--text type="primary" @click="handlePreview(item.url)" v-for="(item, index) in trains" :key='index' :text="item.name"></u--text>
					</view>
				</view>
			</view>
		</view>
		<u-collapse :value="['1']">
			<view class="white-card">
				<u-tabs class="tabs-2" :list="list1" @click="state" lineColor="#333"
					:activeStyle="{color: '#333', transform: 'scale(1)'}"
					:inactiveStyle="{ color: '#666', transform: 'scale(1)'}">
				</u-tabs>
				<link-record
					v-if="tabIndex == 0"
					:docClassList="docClassList"
					:dataList="formData.recordLinks"
					:status="false"
				>
				</link-record>
				<link-file
					v-if="tabIndex == 1"
					:docClassList="docClassList"
					:dataList="formData.docLinks"
				>
				</link-file>
				<historical-version
					v-if="tabIndex == 2"
					:dataList="formData.versions"
				>
				</historical-version>
			</view>
		</u-collapse>
	</view>
</template>

<script>
	import linkRecord from "@/pages/common/linkRecord.vue";
	import linkFile from "@/pages/common/linkFile.vue";
	import historicalVersion from "@/pages/common/historicalVersion.vue";
	import {standardGetDetail} from "@/api/document_account/standard";
	import {linkLoglistlink} from "@/api/file_processing/modifiyApply";
	import { settingDocClassList } from "@/api/file_settings/type_settings";
	export default {
		components: {linkRecord,linkFile,historicalVersion},
		data() {
			return {
				status: undefined,
				formData: {},
				trains: [],
				dataType: undefined,
				docClassList: [],
				docClassTree: [],
				tabIndex: '',
				list1: [{
					name: '关联记录',
				}, {
					name: '关联文件',
				}, {
					name: '文件历史'
				}]
			}
		},
		onLoad(option) {
			let row = option
			this.status = row.status
			this.getDetail(row)
		},
		// onShow() {
		// 	this.getDetail()
		// },
		methods: {
			state(tab) {
				this.tabIndex = tab.index
			},
			getDocClassList(){
				settingDocClassList({classStatus: "1", dataType:this.dataType,neClassType:'foreign'}).then(res => {
				  this.docClassList = uni.$u.deepClone(res.rows);
				  this.docClassTree = this.handleTree(res.rows, "id", "parentClassId")
				});
			},
			formatterDocClass(cellValue){
				let _this = this
			    let item = _this.docClassList.find(item=>item.id===cellValue)
			    return item?item.className:cellValue
			},
			getDetail(query) {
				let _this = this
				_this.detailLoading = true
				standardGetDetail(query).then(async res => {
					let formData = res.data;
					//关联文件
					let res1 = await linkLoglistlink({linkType: "REF_DOC", versionId: formData.versionId})
					formData.docLinks = res1.data
					//关联记录
					let res2 = await linkLoglistlink({linkType: "RECORD", versionId: formData.versionId})
					if (_this.status==="1") {
					  formData.recordLinks = res2.data.filter(item=>item.status===1)
					}else {
					  formData.recordLinks = res2.data
					}
					formData.type = _this.formData.type
					formData.changeType = _this.pButton;
					if(formData.classType===_this.classTypeRecord){
					  _this.activeIndex = '3'
					}
					_this.formData = formData
					let trains=[]
					if (formData.trains&&formData.trains.length>0) {
					  formData.trains.forEach(item=>{
						trains.push({
						  url: item.fileIds,
						  name: item.files[0].fileName
						})
					  })
					}
					_this.trains = trains
					_this.dataType = formData.dataType
					_this.getDocClassList()
				}).finally(()=>{
					_this.detailLoading = false
				});
			},
			handlePreview(fileId){
        let url = `/pages/pdfPreview/index?fileId=${fileId}`
        uni.$u.route(url)
			},
		}
	}
</script>
