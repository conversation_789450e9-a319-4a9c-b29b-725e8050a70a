<template>
	<view class="u-page">

		<view class="white-card">
			<view class="card-head">
				<view class="head-title">基础信息</view>
			</view><!--card-head卡片标题-->
			<view class="card1">
				<view class="text-list table-list">
					<view class="clum">消息内容：</view>
					<view class="text">{{data.docClass}}</view>
				</view>
				<view class="text-list table-list">
					<view class="clum">消息类型：</view>
					<view class="text">{{data.msgClass}}</view>
				</view>


				<view class="text-list table-list">
					<view class="clum">接收时间：</view>
					<view class="text">{{parseTime(data.createTime,'{y}-{m}-{d} {h}:{i}')}}</view>
				</view>

			</view>
		</view>


		<view class="white-card">
			<view class="card-head">
				<view class="head-title">文件信息</view>
			</view><!--card-head卡片标题-->
			<view class="card1">
				<view class="text-list table-list">
					<view class="clum">文件类型：</view>
					<view class="text">{{data.docClass}}</view>
				</view>

				<view class="text-list table-list">
					<view class="clum">文件名称：</view>
					<view class="text">{{data.docName}}</view>
				</view>
				<view class="text-list table-list">
					<view class="clum">文件编号：</view>
					<view class="text">{{data.id}}</view>
				</view>
				<view class="text-list table-list">
					<view class="clum">文件版本：</view>
					<view class="text">{{data.versionValue}}</view>
				</view>
				<view class="text-list table-list">
					<view class="clum">编辑部门：</view>
					<view class="text">{{data.deptName}}</view>
				</view>
				<!-- <view class="text-list table-list">
					<view class="clum">编制人：</view>
					<view class="text">{{data.userName}}</view>
				</view>
				<view class="text-list table-list">
					<view class="clum">申请人：</view>
					<view class="text">{{data.userName}}</view>
				</view>
				<view class="text-list table-list">
					<view class="clum">申请时间：</view>
					<view class="text">{{parseTime(data.createTime,'{y}-{m}-{d} {h}:{i}')}}</view>
				</view>
				<view class="text-list table-list">
					<view class="clum">附件：</view>
						<u--text mode="link" :text="standardDoc ? standardDoc.docName :''"  ></u--text>
						
				</view>
				<view class="text-list table-list">
					<view class="clum">变更要素：</view>
					<view class="text">{{data.changeFactor}}</view>
				</view>
				<view class="text-list table-list">
					<view class="clum">变更原因：</view>
					<view class="text">{{data.applyReason}}</view>
				</view>
				<view class="text-list table-list">
					<view class="clum">变更内容：</view>
					<view class="text">{{data.content}}</view>
				</view>
				<view class="text-list table-list">
					<view class="clum">备注：</view>
					<view class="text">{{data.remark ?data.remark:'暂无'}}</view>
				</view> -->
			</view>
		</view>


	</view>
</template>

<script>
	export default {
		data() {
			return {
				docId: '',
				tabIndex: '',
				changeType: '',
				data: {},
				versions: [],
				depList: [],
				docLinks: [],
				recordLinks: [],
				distributeDepths: [],
				trains: [],
				standardDoc: [],
				trainDept: '',
				status: '',
				data: {},
				applyClass: '',
				serviceType: '',
				list1: [{
					name: '关联文件',
				}, {
					name: '关联记录',
				}, {
					name: '文件历史'
				}]
			}
		},
		onLoad(option) {

			this.data = JSON.parse(decodeURIComponent(option.item));
			this.readNews()
		},
		// onShow() {
		// 	this.getDetail()
		// },
		methods: {
			readNews() {

				this.publicApi.readNews(this.data.id).then()
			},
			state(tab) {
				this.tabIndex = tab.index
			},
			getDetail() {

				let params = {
					docId: this.docId
				}
				this.publicApi.standardGetDetail(params).then(res => {
					this.data = res.data
					this.depList = res.data.distributeDepths
					let trainDeptList = JSON.parse(res.data.trainDept)
					this.trains = res.data.trains
					this.docLinks = res.data.docLinks
					this.recordLinks = res.data.recordLinks
					this.versions = res.data.versions
					this.standardDoc = [],
						this.standardDoc = res.data.standardDoc
					this.trainDept = ''
					trainDeptList.forEach(item => {
						this.trainDept += item.deptName + '、'
					})
					this.trainDept.substring(0, this.trainDept.length - 1)
				})

			},

		}
	}
</script>

<style>

</style>