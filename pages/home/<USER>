<template>
	<view class="u-page">
		<u--form labelPosition="left" :model="user" :rules="rules" ref="form">
			<view class="white-card">
				<u-form-item label="旧密码" borderBottom prop="oldPassword" labelWidth="80">
					<u--input v-model="user.oldPassword" border="none" placeholder="请输入"></u--input>
				</u-form-item>
				<u-form-item label="新密码" borderBottom prop="newPassword" labelWidth="80">
					<u--input v-model="user.newPassword" border="none" placeholder="请输入"></u--input>
				</u-form-item>
				<u-form-item label="确认密码" borderBottom prop="confirmPassword" labelWidth="80">
					<u--input v-model="user.confirmPassword" border="none" placeholder="请输入"></u--input>
				</u-form-item>
			</view><!--white-card 白色卡片-->
			<view class="foot-btn">
				<u-row>
					<u-col span="12">
						<u-button type="primary" @click="submit" :text="$t('doc.this_dept_annex')"></u-button>
					</u-col>
				</u-row>
			</view><!--foot-btn 固定底部按钮-->
		</u--form>
	</view>
</template>

<script>
	import {
		updateUserPwd
	} from "@/api/system/user"
	export default {
		data() {
			return {
				user: {
					oldPassword: undefined,
					newPassword: undefined,
					confirmPassword: undefined
				},
				rules: {
					oldPassword: [{
						required: true,
						message: '旧密码不能为空',
						trigger: ['change', 'blur']
					}],
					newPassword: [{
							required: true,
							message: '新密码不能为空',
							trigger: ['change', 'blur']
						},
						{
							min: 6,
							max: 20,
							message: '长度在 6 到 20 个字符',
							trigger: ['change', 'blur']
						}
					],
					confirmPassword: [{
						required: true,
						message: '确认密码不能为空',
						trigger: ['change', 'blur']
					}, {
						validator: (rule, value, callback) => {
							return this.user.newPassword === value;
						},
						message: '两次输入的密码不一致',
						trigger: ['change', 'blur']
					}]
				}
			};
		},
		onReady() {
			//如果需要兼容微信小程序，并且校验规则中含有方法等，只能通过setRules方法设置规则。
			this.$refs.form.setRules(this.rules)
		},
		methods: {
			submit() {
				this.$refs.form.validate().then(res => {
					updateUserPwd(this.user.oldPassword, this.user.newPassword).then(response => {
						this.$modal.msgSuccess("修改成功")
					})
				})
			}
		}
	};
</script>

<style>

</style>