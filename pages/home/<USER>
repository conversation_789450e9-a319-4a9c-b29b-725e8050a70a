<template>
	<view class="me-page">

		<view class="user-card">
			<image class="user-img" mode="aspectFit" src="@/static/images/user.svg"></image>
			<view class="name">
				<view class="big">{{userInfo.nickName}}</view>
				<view class="small">{{userInfo.userName}}</view>
			</view>
			<view class="text">
				<view class="txt active">{{roles.roleName}}</view>
				<view class="txt">{{deptInfo.deptName}}</view>
			</view>
		</view>

		<u-cell-group>
			<!-- 语言切换 -->
			<u-cell :title="`${$t(`home.language_toggle`)}(${languageLabel})`" @click="show=true">
				<image slot="icon" src="@/static/images/icon1.svg"></image>
				<u-icon slot="right-icon" name="arrow-right"></u-icon>
			</u-cell>
			<!-- 退出登录 -->
			<u-cell :title="$t(`home.layout_system`)" @click="logout">
				<image slot="icon" src="@/static/images/icon2.svg"></image>
			</u-cell>
		</u-cell-group>
		<u-action-sheet :actions="languageOptions" @select="languageChange" :show="show"
			@close="show=false"></u-action-sheet>
	</view>
</template>

<script>
	import {
		getInfo
	} from '@/api/login'
	export default {
		data() {
			return {
				value1: 3,
				signFlag: false,
				userInfo: {},
				deptInfo: {},
				roles: {},
				show: false,
				language: uni.getStorageSync('language'), // 语言类型
			}
		},
		dicts: ['language_switch'],
		computed: {
			languageLabel: function() {
				let item = this.dict.type['language_switch'].find(v => this.language === v.value);
				if (item) {
					return this.dictLanguage(item)
				}
				return null;
			},
			languageOptions: function() {
				return this.dict.type['language_switch'].map((item) => {
					return {
						name: this.dictLanguage(item),
						value: item.value
					}
				})
			}
		},
		onShow() {
			this.getDetail()
		},
		methods: {
		
			languageChange(item) {
				console.log(item);
				this.$i18n.locale = item.value
				uni.setStorageSync('language', item.value)
				uni.removeStorageSync('languageData')
				window.location.reload()
			},
			click1(e) {
				this.value1 = e
			},
			getDetail() {
				getInfo().then(res => {
					this.userInfo = res.data.user
					this.deptInfo = res.data.user.dept
					this.roles = res.data.user.roles[0] || {}
				})
			},
			logout() {
				this.$modal.confirm(this.$t('home.exit_the_system')).then(() => {
					this.$store.dispatch('LogOut').then(() => {
						this.$tab.reLaunch('/pages/index')
					})
				})
			},
			toPassword() {
				this.$tab.reLaunch('/pages/home/<USER>')
			},
		},

	}
</script>

<style>

</style>