<template>
	<view>
		<u-navbar height="0">
			<view class="u-nav-slot" slot="left"> </view>
		</u-navbar><!--u-navbar 去掉导航-->

		<u-sticky class="sticky-2 top-0">
			<u-search placeholder="请输入关键字搜索" v-model="companyParams.docName" @change="getList"
				:showAction="false"></u-search>
		</u-sticky><!--u-sticky 粘性布局 搜索框、筛选-->

		<view class="file-list">
			<view class="list" v-for="(item,index) in companyList" :key="index">
				<view class="list-table">
					<view class="list-ico">
						<image mode="aspectFit" src="../images/word.svg"></image>
					</view>
					<view class="list-text">
						<view class="title gray">{{item.docName}}</view>
						<view class="tag-group">
							<u-tag :text="item.docId" size="mini"></u-tag>
							<u-tag :text="item.versionValue" size="mini"></u-tag>
							<u-tag :text="item.docClass" size="mini"></u-tag>
						</view>
						<u-button @click.native.stop='openCom(item)' icon="more-dot-fill" class="more"></u-button>
						<u-action-sheet @close="closeAct" :closeOnClickOverlay='true' :actions="companyActList"
							@select="selectComClick" :show="companyShow"></u-action-sheet>
					</view>
				</view>
				<view class="list-lab">
					<view class="lab">
						<image mode="aspectFit" src="../images/frame3.png"></image>人力资源部
					</view>
					<view class="lab"><u-icon name="clock" size="15"></u-icon>2022-01-01 生效</view>
				</view>
			</view><!--list-->

		</view><!--list-page 列表-->
	</view>
</template>

<script>
	export default {
		data() {
			return {
				show: false,
				show1: false,
				companyList: [],
				companyShow: false,
				userInfo: {},
				companyParams: {
					pageNum: 1,
					pageSize: 10,
					linkType: "SMP",
					searchValue: "",
					docId: null,
					docName: null,

				},
				companyActList: [{
						name: '借阅申请',
						url: 'pages/my_business/borrow/apply',
						fontSize: '14'
					},
					{
						name: '提建议',
						url: 'pages/my_business/suggest/apply',
						fontSize: '14'
					}
				],
			}
		},
		onLoad(option) {
			this.companyParams.docName = option.docName
			this.getList()
			this.getUserInfo()
		},
		methods: {
			getUserInfo() {
				this.publicApi.getInfo().then(res => {
					this.userInfo = res.data.user
				})
			},
			selectComClick(val) {
				this.companyShow = false
				let url = val.url
				let params = {
					docId: this.item.docId
				}
				uni.$u.route({
					url: url,
					params: params
				})
			},
			openCom(item) {
				this.companyActList = [{
					name: '提建议',
					url: 'pages/my_business/suggest/apply',
					fontSize: '14'
				}]
				if (item.deptId != this.userInfo.dept.deptId &&
					item.trainDept.indexOf(this.userInfo.dept.deptId) < 0
				) {
					this.companyActList = [{
							name: '借阅申请',
							url: 'pages/my_business/borrow/apply',
							fontSize: '14'
						},
						{
							name: '提建议',
							url: 'pages/my_business/suggest/apply',
							fontSize: '14'
						}
					]
				}
				this.item = item
				this.companyShow = true
			},
			closeAct() {
				this.companyShow = false
				this.deptShow = false
			},

			getList() {
				this.publicApi.listVersion(this.companyParams).then(res => {
					this.companyList = res.rows
				})
			}
		}
	}
</script>

<style>

</style>