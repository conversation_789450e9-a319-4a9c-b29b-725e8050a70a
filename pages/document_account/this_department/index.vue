<template>
	<view>
		<u-sticky class="sticky-2">
			<!-- 请输入关键字搜索 -->
			<u-search :placeholder="$t('doc.this_dept_insert_keyword')" v-model="queryParams.searchValue"
				:showAction="false" @change="handleSearch"></u-search>
			<view class="sx-button" style="padding:0">
				<u-tabs class="tabs-2" keyName="className" :list="linkTypeTab" @click="state" lineColor="#333"
					:activeStyle="{color: '#333', transform: 'scale(1)'}"
					:inactiveStyle="{ color: '#666', transform: 'scale(1)'}">
				</u-tabs>
			</view>
		</u-sticky><!--u-sticky 粘性布局 搜索框、筛选-->
		<view class="file-list">
			<view class="list" v-for="(item,index) in dataList" :key="index" @click="">
				<view class="list-table">
					<view class="list-ico">
						<image mode="aspectFit" src="@/static/images/word.svg"></image>
					</view>
					<view class="list-text">
						<view class="title gray">{{item.docName}}</view>
						<view class="tag-group">
							<u-tag :text="item.docId" size="mini"></u-tag>
							<u-tag :text="item.versionValue" size="mini"></u-tag>
							<u-tag :text="formatterDocClass(item.docClass)" size="mini"></u-tag>
						</view>
						<u-button @click="openDep(item)" icon="more-dot-fill" class="more"></u-button>
						<u-action-sheet @close="closeAct" :closeOnClickOverlay='true' :actions="actList"
							@select="selectComClick" :show="show"></u-action-sheet>
					</view>
				</view>
				<view class="list-lab">
					<view class="lab">
						<image mode="aspectFit" src="@/static/images/frame3.png"></image>{{item.deptName}}
					</view>
					<view class="lab"><u-icon name="clock"
							size="15"></u-icon>{{parseTime(item.startDate,'{y}-{m}-{d} {h}:{i}')}}</view>
					<view class="lab"><u-icon name="clock" size="15"></u-icon>{{formatter(statusList,item.status)}}
					</view>
				</view>
			</view><!--list-->
			<u-empty v-if="dataList.length == 0" mode="list" icon="http://cdn.uviewui.com/uview/empty/list.png">
			</u-empty>
			<uni-pagination v-else :pageSize='queryParams.pageSize' @change="handlePageChange"
				:current="queryParams.pageNum" show-icon="true" :total="total"></uni-pagination>
		</view><!--list-page 列表-->
	</view>
</template>

<script>
	import {
		listVersion
	} from "@/api/document_account/version";
	export default {
		name: "ThisDepartment",
		props: {
			linkTypeTab: {
				type: Array,
				default: () => []
			},
			docClassTree: {
				type: Array,
				default: () => []
			},
			docClassList: {
				type: Array,
				default: () => []
			}
		},
		data() {
			return {
				userInfo: this.$store.getters.user,
				item: {},
				dataList: [],
				show: false,
				statusList: [{
						label: this.$t("doc.this_dept_take_effect"), // 生效
						value: '1'
					},
					{
						label: this.$t("doc.this_dept_Invalid"), // 失效
						value: '2'
					},
				],
				actList: [{
					name: '查看详情',
					url: '/pages/detail/index',
					color: '#333',
					fontSize: '14'
				}],
				queryParams: {
					pageNum: 1,
					pageSize: 10,
					searchValue: "",
					docId: null,
					docClass: null,
					docName: null,
					secDeptId: null,
					params: {
						startTime: "",
						endTime: "",
					},
					dataType: "stdd",
					// 有效版本文件 1有效、2失效
					status: '1',
					docClassList: []
				},
				total: 0,
			}
		},
		mounted() {},
		methods: {
			formatter(dataList, value) {
				let item = dataList.find(item => item.value === value)
				return item ? item.label : value
			},
			formatterDocClass(cellValue) {
				let _this = this
				let item = _this.docClassList.find(item => item.id === cellValue)
				return item ? item.className : cellValue
			},
			getDataList() {
				this.dataList = []
				this.queryParams.secDeptId = this.userInfo.dept.secDeptId
				listVersion(this.queryParams).then(res => {
					this.dataList = res.rows
					this.total = res.total;
				})
			},
			openDep(item) {
				this.item = item
				this.show = true
			},
			closeAct() {
				this.show = false
			},
			selectComClick(val) {
				this.show = false
				let url = val.url
				let data = {
					docId: this.item.docId,
					versionId: this.item.versionId,
					flag: 0,
					status: this.queryParams.status
				}
				uni.$u.route(url, data)
			},
			init() {
				this.state({
					index: 0
				})
			},
			state(tab) {
				let t = tab.index;
				let index = this.linkTypeTab[t].id;
				this.varChangeColor = index;
				this.handleSelectNode(this.docClassTree.find(item => item.id === index))
				this.queryParams.docClass = index;
				this.getDataList()
			},
			handleSelectNode(node) {
				let docClassList = []
				if (node) {
					this.getChildrenList(docClassList, node, 'id')
				}
				this.queryParams.docClassList = docClassList
			},
			handleSearch() {
				this.queryParams.pageNum = 1
				this.getDataList()
			},
			handlePageChange({
				type,
				current
			}) {
				this.queryParams.pageNum = current
				this.getDataList()
			},
		},
	}
</script>