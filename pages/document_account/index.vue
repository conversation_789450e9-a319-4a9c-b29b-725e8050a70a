<template>
	<view>
		<u-navbar title="" :bgColor="bgColor" height="0">
			<view class="u-nav-slot" slot="left"> </view>
		</u-navbar><!--u-navbar 去掉导航-->

		<u-sticky class="sticky-1">
			<u-tabs class="tabs-1" :list="tabList" @click="topMenu" lineColor="#f9fafe"
				:activeStyle="{color: '#333', transform: 'scale(1)'}"
				:inactiveStyle="{ color: '#ccc', transform: 'scale(1)'}">
			</u-tabs>
		</u-sticky><!--u-sticky 粘性布局 标签-->

		<company :linkTypeTab="docLinkTypeTab" :docClassTree="docClassTree" :docClassList="docClassList" status="1"
			v-show="topMenuTab == 'gs'" ref="gs"></company>
		<this-department :linkTypeTab="docLinkTypeTab" :docClassTree="docClassTree" v-show="topMenuTab == 'bbm'"
			ref="bbm"></this-department>
		<exterior-door :linkTypeTab="docLinkTypeTab" :docClassTree="docClassTree" v-show="topMenuTab == 'wbm'"
			ref="wbm"></exterior-door>
		<company :linkTypeTab="docLinkTypeTab" :docClassTree="docClassTree" status="2" v-show="topMenuTab == 'sxgs'"
			ref="sxgs"></company>
		<external :linkTypeTab="foreignDocClassList" v-show="topMenuTab == 'wl'" ref="wl"></external>
		<u-back-top :scroll-top="scrollTop" top="300"></u-back-top>
	</view>

</template>

<script>
	import {
		listVersion
	} from "@/api/document_account/version";
	import {
		settingDocClassList
	} from "@/api/file_settings/type_settings";
	import {
		listDept
	} from "@/api/system/dept";
	import company from "@/pages/document_account/company/index.vue";
	import thisDepartment from "@/pages/document_account/this_department/index.vue";
	import exteriorDoor from "@/pages/document_account/exterior_door/index.vue";
	import external from "@/pages/document_account/external/index.vue";
	export default {
		components: {
			company,
			thisDepartment,
			exteriorDoor,
			external
		},
		data() {
			return {
				docClassList: [],
				docClassTree: [],
				docLinkTypeTab: [],
				foreignDocClassList: [],
				bgColor: '',
				title: '',
				scrollTop: 0,
				tabList: [{
					name: this.$t("doc.company_dept_file"), // 公司文件
					permission: 'process:gs:standard:list',
					value: 'gs',
				}, {
					name: this.$t("doc.this_dept"), // 本部门文件
					permission: 'process:bbm:standard:list',
					value: 'bbm',
				}, {
					name: this.$t("doc.exterior_dept"), // 外部门文件
					permission: 'process:wbm:standard:list',
					value: 'wbm',
				}, {
					name: this.$t("doc.invalid_dept"), // 失效公司文件
					permission: 'process:sx:gs:standard:list',
					value: 'sxgs',
				}, {
					name: this.$t("doc.external_file"), // 外来文件
					permission: 'process:wl:standard:list',
					value: 'wl',
				}],
				deptOptions: [],
				topMenuTab: 'gs',
			}
		},
		onLoad(option) {
			this.tabListFilter()
			this.getTabList()
			this.getDeptList()
		},
		methods: {
			tabListFilter() {
				this.tabList = this.tabList.filter(item => this.$auth.hasPermi(item.permission))
				this.topMenuTab = this.tabList[0].value
			},
			getTabList() {
				settingDocClassList({
					classStatus: "1",
					dataType: "stdd",
					neClassType: 'foreign',
					openPurview: true
				}).then(response => {
					this.docClassList = JSON.parse(JSON.stringify(response.rows))
					this.docClassTree = this.handleTree(response.rows.filter(item => item.purview), "id",
						"parentClassId")
					let linkTypeTab = []
					this.docClassTree.forEach(
						(element) => {
							linkTypeTab.push({
								className: element.className,
								id: element.id,
							});
						}
					);
					this.docLinkTypeTab = linkTypeTab
					this.topMenu()
				})
				settingDocClassList({
					classStatus: "1",
					dataType: "stdd",
					parentClassId: "DEO",
					openPurview: true
				}).then(response => {
					this.foreignDocClassList = response.rows
				})
			},
			topMenu(tab) {
				if (tab) {
					this.topMenuTab = tab.value
				}
				this.getDataList()
			},
			getDataList() {
				this.$nextTick(() => {
					this.$refs[this.topMenuTab].init()
				})
			},
			getDeptList() {
				listDept({
					status: 0,
					deptLevel: 2
				}).then((response) => {
					this.deptOptions = this.handleTree(response.data, "deptId");
				});
			},
		},
		onPageScroll(e) {
			this.scrollTop = e.scrollTop;
		}
	}
</script>

<style>

</style>