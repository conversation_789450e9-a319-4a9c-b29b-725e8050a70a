<template>
	<view>
		<u-sticky class="sticky-2">
			<!-- 请输入关键字搜索 -->
			<u-search :placeholder="$t('doc.this_dept_insert_keyword')" v-model="queryParams.title" :showAction="false"
				@change="handleSearch"></u-search>
			<view class="sx-button" style="padding:0">
				<u-tabs class="tabs-2" keyName="label" :list="linkTypeTab" @click="state" lineColor="#333"
					:activeStyle="{color: '#333', transform: 'scale(1)'}"
					:inactiveStyle="{ color: '#666', transform: 'scale(1)'}">
				</u-tabs>
			</view>
		</u-sticky><!--u-sticky 粘性布局 搜索框、筛选-->
		<view class="message-list">
			<view class="list" v-for="(item,index) in dataList" :key="index" @click="toDetail(item)">
				<view class="title">{{item.title}}</view>
				<view class="text">{{item.procDefName}}</view>
				<view class="time">{{parseTime(item.sendTime,'{y}-{m}-{d} {h}:{i}')}}</view>
			</view><!--list-->
			<u-empty v-if="dataList.length == 0" mode="list" icon="http://cdn.uviewui.com/uview/empty/list.png">
			</u-empty>
			<uni-pagination v-else :pageSize='queryParams.pageSize' @change="handlePageChange"
				:current="queryParams.pageNum" show-icon="true" :total="total"></uni-pagination>
		</view><!--list-page 列表-->
	</view>
</template>

<script>
	import {
		workflowToDoList
	} from "@/api/my_business/workflow";
	export default {
		name: "OriginalTodo",
		props: {
			docClassTree: {
				type: Array,
				default: () => []
			},
			docClassList: {
				type: Array,
				default: () => []
			},
			status: {
				type: String,
				default: '1'
			}
		},
		data() {
			return {
				linkTypeTab: [{
						label: this.$t("myItem.handle_pending"), // '待办'
						status: '1',
						value: '1'
					},
					{
						label: this.$t("myItem.handle_done"), // 已办
						status: '2',
						value: '1'
					},
					{
						label: this.$t("myItem.handle_finish"), // 办结
						status: '3',
						value: '1'
					},
				],
				item: {},
				dataList: [],
				show: false,
				statusList: [{
						label: this.$t("doc.this_dept_take_effect"), // 生效
						value: '1'
					},
					{
						label: this.$t("doc.this_dept_Invalid"), // 失效
						value: '2'
					},
				],
				actList: [{
					name: '查看详情',
					url: '/pages/detail/index',
					color: '#333',
					fontSize: '14'
				}],
				queryParams: {
					pageNum: 1,
					pageSize: 10,
					orderByColumn: "sendTime",
					isAsc: "desc",
					title: "",
					status: 1,
				},
				total: 0,
			}
		},
		mounted() {},
		methods: {
			formatter(dataList, value) {
				let item = dataList.find(item => item.value === value)
				return item ? item.label : value
			},
			formatterDocClass(cellValue) {
				let _this = this
				let item = _this.docClassList.find(item => item.id === cellValue)
				return item ? item.className : cellValue
			},
			getDataList() {
				this.loading = true;
				this.dataList = [];
				this.queryParams.searchType = 'original_bpmn'
				workflowToDoList(this.queryParams).then((response) => {
					if (this.activeName == "taskToDo") {
						// 待办
						this.statustotal = response.total;
					}
					if (this.activeName == "taskDealed") {
						// 已办
						this.doneTotal = response.total;
					}
					if (this.activeName == "taskFinish") {
						// 办结
						this.finishedTotal = response.total;

					}
					this.dataList = response.rows;
					this.total = response.total;
					this.loading = false;
				});
			},
			openDep(item) {
				this.item = item
				this.show = true
			},
			closeAct() {
				this.show = false
			},
			toDetail(item) {
				this.show = false
				let url = '/pages/workflowList/addWorkflow/'
				let data = this.getUrlParams(item.url);
				data.status = item.status
				uni.$u.route(url + data.type, data)
			},
			init() {
				this.state({
					index: 0
				})
			},
			state(tab) {
				let t = tab.index;
				let status = this.linkTypeTab[t].status;
				this.queryParams.status = status;
				this.getDataList()
			},
			handleSearch() {
				this.queryParams.pageNum = 1
				this.getDataList()
			},
			handlePageChange({
				type,
				current
			}) {
				this.queryParams.pageNum = current
				this.getDataList()
			},
		},
	}
</script>