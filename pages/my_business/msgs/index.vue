<template>
	<view>

		<u-sticky class="sticky-2">
			<view class="sx-button">
				<u-tabs class="tabs-2" :list="list2" @click="state" lineColor="#333"
					:activeStyle="{color: '#333', transform: 'scale(1)'}"
					:inactiveStyle="{ color: '#666', transform: 'scale(1)'}">
				</u-tabs>
			</view>
		</u-sticky><!--u-sticky 粘性布局 搜索框、筛选-->

		<view class="message-list">
			<view class="list" v-for="(item,index) in messageList" :key="index" @click="toDetail(item)">
				<view class="title">{{item.msgInfo}}</view>
				<view class="time">{{parseTime(item.createTime,'{y}-{m}-{d} {h}:{i}')}}</view>
			</view><!--list-->
			<u-empty v-if="messageList.length == 0" mode="list" icon="http://cdn.uviewui.com/uview/empty/list.png">
			</u-empty>

		</view><!--message-list 消息列表-->

	</view>
</template>

<script>
	import {
		queryNews,
		readNews
	} from "@/api/my_business/news";
	export default {
		data() {
			return {
				linkTypeTab: [{
						label: this.$t("myItem.msg_unread"), // 未读消息
						status: '0',
						value: '1'
					},
					{
						label: this.$t("myItem.msg_read"), // 已读消息
						status: '1',
						value: '1'
					},
				],
				radios: [{
					checked: true
				}, {
					checked: false
				}],
				queryParams: {
					msgStatus: "0",
					pageNum: 1,
					pageSize: 1000,
				},
				messageList: [],
				list2: [{
					name: this.$t("myItem.msg_unread"), // 未读消息
					value: 0,
				}, {
					name: this.$t("myItem.msg_read"), // 已读消息
					value: 1,
				}]
			}
		},
		onLoad() {},
		methods: {
			init() {
				this.state({
					index: 0
				})
			},
			getDataList() {
				this.messageList = []
				queryNews(this.queryParams).then(res => {
					this.messageList = res.data.rows
				})
			},
			radioClick(name) {
				this.radios.map((item, index) => {
					item.checked = index === name ? true : false
				})
			},
			state(tab) {
				let t = tab.index;
				let status = this.linkTypeTab[t].status;
				this.queryParams.msgStatus = status;
				this.getDataList()
			},
			toDetail(item) {
				let url = 'pages/home/<USER>'
				uni.$u.route({
					url: url,
					params: {
						item: encodeURIComponent(JSON.stringify(item)),
					}
				})
			},
		}
	}
</script>

<style>

</style>