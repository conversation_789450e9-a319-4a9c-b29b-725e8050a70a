<template>
	<view>
		<u-navbar title="" :bgColor="bgColor" height="0">
			<view class="u-nav-slot" slot="left"> </view>
		</u-navbar><!--u-navbar 去掉导航-->

		<u-sticky class="sticky-1">
			<u-tabs class="tabs-1" :current="current" :list="tabList" @click="topMenu" lineColor="#f9fafe"
				:activeStyle="{color: '#333', transform: 'scale(1)'}"
				:inactiveStyle="{ color: '#ccc', transform: 'scale(1)'}">
			</u-tabs>
		</u-sticky><!--u-sticky 粘性布局 标签-->

		<original-todo :linkTypeTab="docLinkTypeTab" :docClassTree="docClassTree" :docClassList="docClassList"
			status="1" v-show="topMenuTab == 'wdbl'" ref="wdbl"></original-todo>
		<msgs v-show="topMenuTab == 'wdxx'" ref="wdxx"></msgs>
		<u-back-top :scroll-top="scrollTop" top="300"></u-back-top>
	</view>

</template>

<script>
	import {
		listVersion
	} from "@/api/document_account/version";
	import {
		settingDocClassList
	} from "@/api/file_settings/type_settings";
	import {
		listDept
	} from "@/api/system/dept";
	import originalTodo from "@/pages/my_business/original_todo/index.vue"
	import msgs from "@/pages/my_business/msgs/index.vue"
	export default {
		components: {
			originalTodo,
			msgs
		},
		data() {
			return {
				docClassList: [],
				docClassTree: [],
				docLinkTypeTab: [],
				foreignDocClassList: [],
				bgColor: '',
				title: '',
				scrollTop: 0,
				tabList: [{

					name: this.$t("myItem.handle"), // '我的办理'
					value: 'wdbl',
				}, {
					name: this.$t("home.quick_action_my_msg"), // '我的消息'
					value: 'wdxx',
				}],
				deptOptions: [],
				current: 0,
				topMenuTab: 'wdbl',
			}
		},
		onLoad(option) {
			this.getTabList()
			this.getDeptList()
		},
		onShow() {
			let data = this.$tab.getData()
			if (data && data.type) {
				this.topMenuTab = data.type
				this.current = this.tabList.findIndex(item => item.value == data.type)
			}
		},
		methods: {
			tabListFilter() {
				this.tabList = this.tabList.filter(item => this.$auth.hasPermi(item.permission))
				this.topMenuTab = this.tabList[0].value
			},
			getTabList() {
				settingDocClassList({
					classStatus: "1",
					dataType: "stdd",
					neClassType: 'foreign',
					openPurview: true
				}).then(response => {
					this.docClassList = JSON.parse(JSON.stringify(response.rows))
					this.docClassTree = this.handleTree(response.rows.filter(item => item.purview), "id",
						"parentClassId")
					let linkTypeTab = []
					this.docClassTree.forEach(
						(element) => {
							linkTypeTab.push({
								className: element.className,
								id: element.id,
							});
						}
					);
					this.docLinkTypeTab = linkTypeTab
					this.topMenu()
				})
				settingDocClassList({
					classStatus: "1",
					dataType: "stdd",
					parentClassId: "DEO",
					openPurview: true
				}).then(response => {
					this.foreignDocClassList = response.rows
				})
			},
			topMenu(tab) {
				if (tab) {
					this.topMenuTab = tab.value
				}
				this.getDataList()
			},
			getDataList() {
				this.$nextTick(() => {
					this.$refs[this.topMenuTab].init()
				})
			},
			getDeptList() {
				listDept({
					status: 0,
					deptLevel: 2
				}).then((response) => {
					this.deptOptions = this.handleTree(response.data, "deptId");
				});
			},
		},
		onPageScroll(e) {
			this.scrollTop = e.scrollTop;
		}
	}
</script>

<style>

</style>