<template>
	<view>
		<u-sticky class="sticky-2">
			<!-- 输入关键字搜索 -->
			<u-search :placeholder="$t('doc.this_dept_insert_keyword')" v-model="keyWord" :showAction="false"
				@search="toSearch"></u-search>
		</u-sticky><!--u-sticky 粘性布局 搜索框、筛选-->

		<view class="index-page">
			<view class="menu-list">
				<!-- 	<view class="list" @click="toSuggest">
					<image mode="aspectFit" src="@/static/images/index-icon3.svg"></image>
					<view class="title">提建议</view>
				</view>
				<view class="list" @click="toBorrowApply">
					<image mode="aspectFit" src="@/static/images/index-icon2.svg"></image>
					<view class="title">借阅申请</view>
				</view>
				<view class="list" @click="toBorrowList">
					<image mode="aspectFit" src="@/static/images/index-icon7.svg"></image>
					<view class="title">借阅文件</view>
				</view> -->

				<view class="list" @click="toMyBusiness('wdxx')">
					<view class="tag">{{messageTotal}}</view>
					<image mode="aspectFit" src="@/static/images/index-icon1.svg"></image>
					<view class="title">{{$t('home.quick_action_my_msg')}}</view>
				</view><!--list-->
			</view><!--menu-list-->

			<view class="notice">
				<u-notice-bar :text="messageList" v-if="messageTotal>0" direction='column' mode="closable" speed="250"
					url="/pages/componentsB/tag/tag">

				</u-notice-bar>
			</view><!--notice-->

			<view class="index-card">
				<view class="card-head">
					<!-- 待办事项 -->
					<view class="head-title">{{$t('home.my_to_do')}}
						<view class="tag">{{total}}</view>
					</view>
					<view class="head-btn" @click="toMyBusiness('wdbl')"><a
							class="more">{{$t('doc.this_dept_more')}}<u-icon name="arrow-right"></u-icon></a></view>
				</view>
				<view class="index-list" v-if="serviceList.length>0">
					<view class="list" v-for="(item,index) in serviceList" :key="index" @click="toDetail(item)">
						<view class="list-img">
							<image mode="aspectFit" src="@/static/images/index-icon5.svg"></image>
						</view>
						<view class="list-text">
							<view class="title-time">
								<view class="title">{{item.title}}</view>
								<view class="time">{{parseTime(item.sendTime,'{y}-{m}-{d} {h}:{i}')}}</view>
							</view>
							<view class="text">{{item.procDefName}}</view>
						</view>
					</view><!--list-->

				</view><!--index-list-->
				<u-empty v-else mode="list" icon="http://cdn.uviewui.com/uview/empty/list.png">
				</u-empty>
			</view><!--index-card-->

			<view class="index-card">
				<view class="card-head">
					<!-- 最近查看 -->
					<view class="head-title">{{$t('home.recent_view')}}</view>
				</view>
				<view class="index-list" v-if="readList.length>0">
					<view class="list" v-for="(item,index) in readList" :key="index"
						@click="handlePreview(item.fileId)">
						<view class="list-img">
							<image mode="aspectFit" src="@/static/images/index-icon6.svg"></image>
						</view>
						<view class="list-text">
							<view class="title">{{item.docName}}</view>
							<view class="tag-group">
								<u-tag :text="item.docId" size="mini"></u-tag>
								<u-tag :text="item.versionValue" size="mini"></u-tag>
								<u-tag :text="item.deptName" size="mini"></u-tag>
							</view>
						</view>
					</view><!--list-->

				</view><!--index-list-->
				<u-empty v-else mode="list" icon="http://cdn.uviewui.com/uview/empty/list.png">
				</u-empty>
			</view><!--index-card-->
		</view><!--index-page-->

	</view>
</template>

<script>
	import {
		workflowToDoList
	} from "@/api/my_business/workflow";
	import {
		indexSelectReadList,
		indexSelectMessage
	} from "@/api/index/select";
	export default {
		data() {
			return {
				//消息列表
				messageList: [],
				//待办列表
				serviceList: [],
				//我的消息 未读数量
				messageTotal: 0,
				//待办事项 数量
				total: 0,
				keyWord: '',
				readList: [],
				queryParams: {
					pageNum: 1,
					pageSize: 10,
					title: "",
					status: 1,
				},
			}
		},
		onShow() {
			this.getServiceList()
			this.getMessageList()
			this.getReadList()
		},
		methods: {
			toSearch() {
				let url = 'pages/home/<USER>'
				uni.$u.route({
					url: url,
					params: {
						docName: this.keyWord
					}
				})
			},
			getReadList() {
				indexSelectReadList({}).then(res => {
					this.readList = res.data
				})
			},
			getMessageList() {
				indexSelectMessage({
					msgStatus: "0",
					pageNum: 1,
					pageSize: 1000,
				}).then(res => {
					let messageList = []
					if (res.data.rows && res.data.rows.length > 0) {
						messageList = res.data.rows.map(item => item.msgInfo);
					}
					this.messageList = messageList
					this.messageTotal = res.data.rows.length
				})
			},
			getServiceList() {
				this.serviceList = []
				workflowToDoList(this.queryParams).then(response => {
					this.serviceList = response.rows;
					this.total = response.total;
				})
			},
			toDetail(item) {
				this.show = false
				let url = '/pages/workflowList/addWorkflow/'
				let data = this.getUrlParams(item.url);
				data.status = item.status
				uni.$u.route(url + data.type, data)
			},
			handlePreview(fileId) {
        let url = `/pages/pdfPreview/index?fileId=${fileId}`
        uni.$u.route(url)
			},
			toMyBusiness(type) {
				let url = '/pages/my_business/index'
				this.$tab.switchTab(url, {
					type: type
				})
			},
			toSuggest() {
				let url = 'pages/my_business/suggest/apply'
				uni.$u.route({
					url: url,
					params: {}
				})
			},
			toBorrowList() {
				let url = 'pages/my_business/borrow/list'
				uni.$u.route({
					url: url,
					params: {}
				})
			},
			toBorrowApply() {
				let url = 'pages/my_business/borrow/apply'
				uni.$u.route({
					url: url,
					params: {}
				})
			},
		},

	}
</script>

<style>

</style>
