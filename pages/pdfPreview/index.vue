<template>
	<view class="drawer">
		<view class="drawer-head">
			<view class="title">
				文件预览
			</view>
			<view class="btn">
				<u-button @click="close">返回</u-button>
				<u-button @click="pickerShow = true">{{pageNum}}/{{pageTotal}}</u-button>
				<u-button @click="handleRotate">旋转</u-button>
			</view>
		</view>
		<view id="box" @touchstart="handleMouseDown" @touchmove="handleMouseMove" @touchend="handleMouseUp"
			@touchcancel="handleMouseUp" @scroll="handleScroll">
			<view id="printFrom" :style="{width:pdfWidth+pdfWidthUnit}">
				<pdf ref="pdf" v-for="item in pageTotal" :rotate="pageRotate" :id="'pdf'+item" :src="src" :key="item"
					:page="item"></pdf>
			</view>
		</view>
		<u-picker :show="pickerShow" :columns="columns" :title="pageNum+'/'+pageTotal" @cancel="pickerClose"
			@close="pickerClose" @confirm="pickerConfirm" closeOnClickOverlay></u-picker>
	</view>
</template>
<script>
	import {
		listFilePdf
	} from "@/api/file_processing/basicFilePdf.js";
	import {
		personalPreview
	} from "@/api/doc_preview/previewOnline.js";
	// import printJS from 'print-js'
	import {
		previewPdfWatermark
	} from '@/api/file_processing/fileSignature'
	import pdf from 'vue-pdf/src/vuePdfNoSssNoWorker.vue'
	export default {
		name: "PdfPreview",
		props: {},
		components: {
			pdf
		},
		data() {
			return {
				pickerShow: false,
				columns: [],
				pageRotate: 0,
				pageNum: 1,
				pdfWidth: 80,
				pdfWidthUnit: '%',
				percentage: 100,
				loading: false,
				href: "",
				src: "",
				pageTotal: 1,
				status: false,
				touches: undefined,
			}
		},
		mounted() {

		},
		onLoad(option) {
			let fileId = option.fileId
			this.init(fileId)
		},
		methods: {
			close() {
				uni.navigateBack()
			},
			init(id, source) {
				let self = this
				// 默认为公司文件
				let sourceValue = 'COMPANY';
				if (source) {
					sourceValue = source;
				}
				let query = {
					fileId: id,
					status: 'YES'
				}
				listFilePdf(query).then((res) => {
					let encryptPdfArr = res.rows.filter((x) => x.pdfType == "signature")
					let mergePdfArr = res.rows.filter((x) => x.pdfType == "merge")
					let transPdfArr = res.rows.filter((x) => x.pdfType == "transition")
					let pdfId = null
					if (encryptPdfArr.length > 0) {
						// 优先打开签章的PDF文件
						pdfId = encryptPdfArr[0].pdfId
					} else if (mergePdfArr.length > 0) {
						// 次之打开合稿的PDF文件
						pdfId = mergePdfArr[0].pdfId
					} else if (transPdfArr.length > 0) {
						// 次之打开普通转换的PDF文件
						pdfId = transPdfArr[0].pdfId
					} else {
						if (res.rows.length > 0) {
							pdfId = res.rows[0].pdfId
						}
						console.log("此fileId未找到对应的转换PDF、签章PDF文件，fileId=" + id)
					}
					if (pdfId) {
						self.getpersonalPreview(id, sourceValue);
						self.handleOpenView(pdfId)
					}
				});
			},
			// 记录个人阅知文件记录
			getpersonalPreview(id, source) {
				personalPreview({
					id: id,
					source: source
				}).then((res) => {
					console.log(res);
				});
			},
			handlePrint() {
				printJS({
					printable: this.href
				})
			},
			handleOpenView(pdfId) {
				this.loading = true
				previewPdfWatermark(pdfId).then(async stream => {
					// 正常预览
					const URL = window.URL || window.webkitURL;
					const href = URL.createObjectURL(new Blob([stream], {
						type: 'application/pdf;charset=utf-8'
					}))
					this.href = href
					this.src = `${this.viewerUrl}?file=${encodeURIComponent(this.href)}`
					this.src = pdf.createLoadingTask(href)
					// 获取页码
					let promise = await this.src.promise
					this.pageTotal = promise.numPages
					this.columns = [Array.from({
						length: this.pageTotal
					}, (v, k) => k + 1)];
				}).finally(() => {
					this.loading = false
				})
			},
			closeDrawer() {
				this.$emit("close")
			},
			handleChange(event) {
				console.log(event)
			},
			handleMouseDown(event) {
				this.status = true
				this.touches = event.touches
			},
			handleMouseMove(event) {
				if (this.status) {
					if (event.touches.length === 2) {
						let scale = (this.getDistance(event.touches[0], event.touches[1]) / this.getDistance(this.touches[
							0], this.touches[1])).toFixed(3);
						this.handleChange(scale)
						this.touches = event.touches
					}
				}
			},
			handleMouseUp(event) {
				if (event.touches.length === 1) {
					this.status = false
				}
			},
			getDistance(p1, p2) {
				let x = p2.pageX - p1.pageX
				let y = p2.pageY - p1.pageY
				return Math.sqrt((x * x) + (y * y))
			},
			handleChange(scale) {
				let pdfWidth = this.pdfWidth * scale
				if (pdfWidth < 50) {
					pdfWidth = 50
				} else if (pdfWidth > 300) {
					pdfWidth = 300
				}
				this.pdfWidth = pdfWidth
			},
			pickerClose() {
				this.pickerShow = false
			},
			pickerConfirm(item) {
				this.pickerShow = false
				this.handlePageNum(item.value[0])
			},
			handlePageNum(currentValue, oldValue) {
				let offsetTop = document.getElementById('pdf' + currentValue).offsetTop
				document.getElementById('box').scroll({
					top: offsetTop,
				});
			},
			handleScroll() {
				let scrollTop = document.getElementById('box').scrollTop
				for (let index = 0; index < this.pageTotal; index++) {
					let id = index + 1
					let offsetTop = document.getElementById('pdf' + id).offsetTop
					if (scrollTop < offsetTop) {
						if (this.pageNum != index) {
							this.pageNum = index
						}
						return
					}
				}
				if (this.pageNum !== this.pageTotal) {
					this.pageNum = parseInt(this.pageTotal)
				}
			},
			handleRotate() {
				this.pageRotate += 90
			},
		}
	}
</script>
<style scoped>
	#printFrom {
		margin: 0px auto;
		position: relative;
	}

	#box {
		width: 100%;
		height: 100%;
		overflow: auto !important;
		padding: 6px;
		position: relative;
	}

	#printFrom span {
		margin-top: 6px;
		box-shadow: 0px 0px 5px
	}

	.drawer {
		overflow: hidden;
		width: 100%;
		height: 100%;
		background-color: #525659;
		position: absolute;
	}

	.drawer-head {
		height: 35px;
		width: 100%;
		padding-left: 16px;
		padding-right: 16px;
		background-color: #323639;
		border-bottom: none;
		box-shadow: 0px 0px 5px;
		position: sticky;
		top: 0;
	}

	.drawer-head .title {
		color: #ffffff;
		float: left;
		line-height: 35px;
	}

	.drawer-head .btn {
		color: #ffffff;
		text-align: center;
		line-height: 35px;
	}

	.drawer-head .btn .u-button {
		float: right;
		padding: 0;
		width: 50px;
		height: 25px;
		margin: 5px;
	}
</style>