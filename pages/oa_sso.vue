<template>
  <view class="sso-page"></view>
</template>

<script>
import { setToken, removeToken, getToken } from "@/utils/auth";
export default {
  name: "ssoCheck",
  data() {
    return {
    };
  },
  mounted() {
	 
    // 开始执行SSO检查
    this.ssoCheck()
  },
  methods: {
    ssoCheck() {
      let self = this
      // 删除令牌和清理会话信息
      removeToken()
      // 爱数用户令牌
      let token = self.$route.query.token;
      // 指定展示页面URL
      let redirect = self.$route.query.redirect;
      if( token&&redirect) {
        console.log("oa单点登录到>>>>>>>>>>",decodeURIComponent(redirect))
        // SSO验证通过
		setToken(token)
        // document.location.href =  redirect
		let url = decodeURIComponent(redirect)
		// 将pc端地址，替换成移动端【/workflow?businessKey=1836777481881645057&... ---> /pages/workflowList/addWorkflow/update_doc?businessKey=1836777481881645057&... 】
        const regex = /type=([^&]+)/;
        const match = url.match(regex);
		if (match) {
			url = url.replace("/workflow?",`/pages/workflowList/addWorkflow/${match[1]}?`)
			console.log("跳转到:",url)
			uni.$u.route(url)
		} else {
		    console.error('地址错误');
		}
        
      }else{
        alert("非法访问")
      }

    },
  },
};
</script>
