<template>
  <u-collapse >
  	<u-collapse-item title="审批记录" name="1"  >
  		<view class="" v-if="dataList !==undefined && dataList != null  && dataList.length > 0">
  			<view class="line-list" v-for="(item,index) in dataList" :key='index'>
  				<view :class="item.pass?'list success':'list lose'">
  					<view class="point">
						<u-icon :name="item.pass?'checkmark':'close'"></u-icon>
					</view>
  					<view class="title-time"><view class="title">{{item.actDefName}}</view><view class="time">{{parseTime(item.createTime, '{y}-{m}-{d} {h}:{i}')}}</view></view>
  					<view class="text">
  						<view class="txt">{{item.opinion}}</view>
  						<view class="lab">{{item.deptName}}/{{ item.nickName }}</view>
  					</view>
  				</view>
  				
  			</view>
  		</view>
  		<u-empty v-else
  				mode="list"
  				icon="http://cdn.uviewui.com/uview/empty/list.png"
  		>
  		</u-empty>
  	</u-collapse-item>			
  </u-collapse>
</template>

<script>
import { shlkSelectLogByProcInstId } from "@/api/my_business/workflow";

export default {
  name: "WorkflowLogs",
  props: ["procInstId"],
  data() {
    return {
      loading: true,
      dataList: [],
      passList: { 'true':{label:'通过',color:'#70B603'},'false':{label:'不通过',color:'#D9001B'}},
    };
  },
  watch: {
    procInstId (val) {
      if (val) {
        this.getList(val)
      }
    },
  },
  mounted() {
    if (this.procInstId) {
      this.getList(this.procInstId)
    }
  },
  methods: {
    /** 查询岗位列表 */
    getList(procInstId) {
      this.loading = true;
      shlkSelectLogByProcInstId(procInstId).then(response => {
        this.dataList = response.data;
        this.loading = false;
      });
    },
  }
};
</script>
