<template>
	<view class="ft-card">
	
		<view class="ctr" v-for="(item, index) in dataList" :key="index">
			<!-- 变更类型 -->
			<view class="clist">
				<view class="clum">{{$t(`doc.this_dept_change_type`)}}</view>
				<view class="text">{{ formatter(dict.type.business_status,item.changeType) }}</view>
			</view>
			<!-- 版本 -->
			<view class="clist">
				<view class="clum">{{$t(`doc.this_dept_ver`)}}</view>
				<view class="text">
					{{item.versionValue}}
				</view>
			</view>
			<!-- 变更来源 -->
			<view class="clist">
				<view class="clum">{{$t(`doc.this_dept_change_source`)}}</view>
				<view class="text">{{ item.invokeId }}</view>
			</view>
			<!-- 变更原因 -->
			<view class="clist">
				<view class="clum">{{$t(`doc.this_dept_change_reason`)}}</view>
				<view class="text">{{ item.changeReason }}</view>
			</view>
			<!-- 变更内容 -->
			<view class="clist">
				<view class="clum">{{$t(`doc.this_dept_changes`)}}</view>
				<view class="text">{{item.content}}</view>
			</view>
			<!-- 生效日期 -->
			<view class="clist">
				<view class="clum">{{$t(`doc.this_dept_effective_date`)}}</view>
				<view class="text">{{ parseTime(item.startDate,"{y}-{m}-{d}") }}</view>
			</view>
			<!-- 发布日期 -->
			<view class="clist">
				<view class="clum">{{$t(`doc.this_dept_release_date`)}}</view>
				<view class="text">{{ parseTime(item.releaseTime,"{y}-{m}-{d}") }}</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "LinkRecord",
		dicts: ['business_status'],
		props: {
			dataList: {
				type: Array,
				default: () => []
			},
		},
		data() {
			return {

			}
		},
		mounted() {

		},
		methods: {
			formatterDocClass(cellValue) {
				let _this = this
				let item = _this.docClassList.find(item => item.id === cellValue)
				return item ? item.className : cellValue
			},
			formatter(dataList, value) {
				let item = dataList.find(item => item.value === value)
				return item ? item.label : value
			}
		},
	}
</script>
