<template>
	<view class="ft-card">
		<view class="ctr" v-for="(item, index) in dataList" :key="index">
			<!-- 文件类型 -->
			<view class="clist">
				<view class="clum">{{$t(`myItem.borrow_file_type`)}}</view>
				<view class="text">{{ formatterDocClass(item.docClass) }}</view>
			</view>
			<!-- 文件名称 -->
			<view class="clist">
				<view class="clum">{{$t(`myItem.borrow_file_name`)}}</view>
				<view class="text">
					<u--text :text="item.docName"></u--text>
				</view>
			</view>

			<!-- 文件编号 -->
			<view class="clist">
				<view class="clum">{{$t(`myItem.borrow_file_id`)}}</view>
				<view class="text">{{ item.docId }}</view>
			</view>
			<!-- 文件版本 -->
			<view class="clist">
				<view class="clum">{{$t(`myItem.borrow_file_ver`)}}</view>
				<view class="text">{{ item.versionValue }}</view>
			</view>
			<!-- 当前状态 -->
			<view class="clist" v-if="status">
				<view class="clum">{{$t(`doc.this_dept_current_file_status`)}}</view>
				<view class="text">{{ formatter(isDeletedList,item.isDeleted)}}</view>
			</view>
			<!-- 待生效状态 -->
			<view class="clist " v-if="status">
				<view class="clum">{{$t(`doc.this_dept_post_release_status`)}}</view>
				<view class="text">{{ formatter(statusList,item.status)}}</view>
			</view>
			<!-- 生效日期 -->
			<view class="clist" v-if="!status">
				<view class="clum">{{$t(`doc.this_dept_effective_date`)}}</view>
				<view class="text">{{parseTime(item.startDate,"{y}-{m}-{d}")}}</view>
			</view>
			<!-- 发布日期 -->
			<view class="clist" v-if="!status">
				<view class="clum">{{$t(`doc.this_dept_release_date`)}}</view>
				<view class="text">{{parseTime(item.releaseTime,"{y}-{m}-{d}")}}</view>
			</view>
		</view>
	</view>

</template>

<script>
	import {
		settingDocClassList
	} from "@/api/file_settings/type_settings";
	export default {
		name: "LinkRecord",
		props: {
			dataList: {
				type: Array,
				default: () => []
			},
		},
		data() {
			return {
				docClassList: [],
				docClassTree: [],
				isDeletedList: [{
						value: 0,
						label: this.$t(`doc.this_dept_pending`) //  '待生效'
					},
					{
						value: 1,
						label: this.$t(`doc.this_dept_in_effect`) // '生效中'
					},
					{
						value: 2,
						label: this.$t(`doc.this_dept_lost_effect`) //'已失效'
					},
				],
				statusList: [{
						value: 1,
						label: this.$t(`doc.this_dept_take_effect`) //'生效'
					},
					{
						value: 2,
						label: this.$t(`doc.this_dept_Invalid`) //'失效'
					},
				]
			}
		},
		mounted() {
			this.getDocClassList()
		},
		methods: {
			getDocClassList() {
				// 获取文件分类集合
				settingDocClassList({
					classStatus: "1",
					neClassType: 'foreign'
				}).then(res => {
					this.docClassList = JSON.parse(JSON.stringify(res.rows))
					this.docClassTree = this.handleTree(res.rows, "id", "parentClassId")
				});
			},
			formatterDocClass(cellValue) {
				let _this = this
				let item = _this.docClassList.find(item => item.id === cellValue)
				return item ? item.className : cellValue
			},
			formatter(dataList, value) {
				let item = dataList.find(item => item.value === value)
				return item ? item.label : value
			}
		},
	}
</script>