<template>
	<view class="white-card">
		<view class="card-head">
			<!-- 选择下一环节及人员 -->
			<view class="head-title">{{$t(`doc.this_dept_select_next`)}}</view>
		</view>
		<u--form labelPosition="left" labelWidth='30%' :model="formSubmit" :rules="formrules" ref="formSubmit">
			<!-- 办理步骤 -->
			<u-form-item required :label="$t(`doc.this_dept_process_step`)" prop="actDefName" borderBottom
				@click="show = true">
				<view>{{nextData.actDefName}}</view>
				<u-icon slot="right" name="arrow-right"></u-icon>
				<u-action-sheet :actions="processList" @select="processNodeClick" :show="show"></u-action-sheet>
			</u-form-item>
			<u-form-item required :label="$t(`doc.this_dept_user_to_be_selected`)" prop="pass" borderBottom
				@click="userShow = true"
				v-show="processList&&processList.length>0&&nextData&&nextData.actDefName&&nextData.actDefType != 'endEvent'">
				<view><u-tag v-for="(item,index) in receiveUserList" :key="index" :text="item.name" plain> </u-tag>
				</view>
				<u-icon slot="right" name="arrow-right"></u-icon>
			</u-form-item>
		</u--form>
		<u-popup :show="userShow" @close="userShow = false">
			<u-row>
				<u-col :span="6">
					<view class="card-head">
						<!-- 待选用户 -->
						<view class="head-title">{{$t(`doc.this_dept_user_to_be_selected`)}}</view>
					</view>
					<DaTreeVue2 style="height: 200px;" ref="DaTreeRef" :showCheckbox="nextData.multi" :data="userList"
						labelField="name" valueField="id"
						@change="(allCheckedKeys, currentItem)=>userNodeClick(currentItem.originItem)"
						checkboxPlacement="none" defaultExpandAll :onlyRadioLeaf="true" />
				</u-col>
				<u-col :span="6">
					<view class="card-head">
						<!-- 已选用户 -->
						<view class="head-title">{{$t(`doc.this_dept_user_be_selected`)}}</view>
					</view>
					<DaTreeVue2 style="height: 200px;" ref="DaTreeRef" :data="receiveUserList" labelField="name"
						valueField="id" checkboxPlacement="none" defaultExpandAll :highlightcCurrentRow="false"
						@cancelClick="removeData" :cancelButton="true" />
				</u-col>
			</u-row>

		</u-popup>
	</view>
</template>

<script>
	import {
		workflowNextactsNew2,
		getNextactuserByPending,
		getNextActUsersByNew,
		getExtAttributeModel,
		selectUserStyle
	} from "@/api/my_business/workflow";
	import DaTreeVue2 from '@/components/da-tree-vue2/index.vue'
	export default {
		name: "Processcode",
		components: {
			DaTreeVue2
		},
		props: {
			selected: {
				type: Boolean,
				default: false,
			},
			order: {
			    type: String,
			    default: '0',
			},
			userListStatus: {
				type: Boolean,
				default: false,
			},
			searchQuery: {
				type: Object,
				default: {},
			},
			hideNodeCode: {
				type: Array,
				default: () => [],
			},
			isSummary: {
				type: Boolean,
				default: true,
			},
			defaultStaff: {
				type: Array,
				default: () => [],
			},
			data: {},
			title: {},
			detatailsData: {},
			promptContent: {
				type: String,
				default: "",
			},
		},
		data() {
			return {
				show: false,
				userShow: false,
				pListData: {},
				summary: "",
				editStatus: true,
				formSubmit: {
					summary: ""
				},
				processList: [],
				processProps: {
					children: "children",
					label: "actDefName",
				},
				userProps: {
					children: "children",
					label: function(data, node) {
						if (data.type == "USER") {
							return data.id + ' ' + data.name
						} else {
							return data.name
						}
					}
				},
				formrules: {
					summary: [{
						required: true,
						message: " ",
						trigger: "change"
					}],
					pass: [{
						required: true,
						message: " ",
						trigger: "change"
					}],
				},
				userTreeName: undefined,
				receiveUserList: [],
				userList: [],
				loading: true,
				nextData: {},
				procDefKey: "",
				actionType: "",
				wf_actionType: "",
				actDefType: "", //actDefType: "endEvent" 判断流程到哪一步了
				pass: "", //是否通过
				kuozhanshuju: "",
				istongguo: false,
				morendta: undefined,
				userInfo: this.$store.getters.user
			};
		},
		created() {
			//this.$emit("yingchanbohui", false);
		},
		watch: {
			actionType: function(val) {
				this.wf_actionType = val;
			},
			"formSubmit.summary": function(val) {
				this.summary = val;
			},
			pass(val) {
			},
			// 根据名称筛选树
			userTreeName(val) {
				this.$refs.userTree.filter(val);
			}
		},
		mounted() {},
		methods: {
			init(pListData) {
				this.pListData = pListData
				this.getOptionsList();
				if (this.pListData.procDefKey != "") {
					this.procDefKey = this.pListData.procDefKey;
				}
			},
			getOptionsList() {
				let _this = this
				let searchQuery = _this.searchQuery
				searchQuery.procDefId = _this.pListData.procDefId
				if (!!_this.pListData.curActInstId) {
					searchQuery.curActInstId = _this.pListData.curActInstId
					// 增加参数：当前环节定义ID【用于步骤已执行标记】
					searchQuery.curActDefId = _this.pListData.curActDefId
				} else {
					searchQuery.curActDefId = _this.pListData.actDefId
				}
				// 增加参数：流程实例ID【用于步骤已执行标记】
				searchQuery.procInstId = _this.pListData.procInstId
				_this.loading = true
				workflowNextactsNew2(searchQuery)
					.then((res) => {
						_this.processList = res.data.filter(item => !_this.hideNodeCode.includes(item.actDefId));
						_this.processList.forEach(item => {
							item.name = item.actDefName
						})
						if (searchQuery.pass!==undefined) {
							let order = parseInt(_this.order)
							_this.processList = _this.processList.filter(item=>searchQuery.pass?(item.actDefOrder>order||item.actDefType==='endEvent'):(item.actDefOrder<order&&item.actDefType!=='endEvent'));
						}
						// 关闭遮罩
						_this.loading = false
						// 自动识别是否默认选中第一个步骤
						if (_this.processList.length > 0) {
							if (_this.processList[0].variables.extend_SelectEnable) {
								// 默认选中第一个步骤
								// 此方法内有打开遮罩和关闭遮罩
								_this.processNodeClick(_this.processList[0]);
								_this.actDefType = res.data[0].actDefType;
							}
						}else{
							this.nextData = {}
						}
					})
					.catch((e) => {
						_this.loading = false;
					});
			},
			processNodeClick(val) {
				uni.showLoading({
					mask:true
				})
				let _this = this
				_this.show = false
				_this.pListData.actDefOrder = val.actDefOrder
				if (!!_this.pListData.curActInstId) {
					_this.processNodeClick1(val);
				} else {
					_this.processNodeClick2(val);
				}
			},
			//流程节点点击触发 带出可选成员
			processNodeClick1(val) {
				let _this = this
				let user_info = this.userInfo
				if (this.nextData != "") {
					if (val.actDefName == this.nextData.actDefName) {
						uni.hideLoading()
						return;
					}
				}
				this.nextData = val;

				this.userList = []; //代选
				this.receiveUserList = []; //已选
				let params = {
					userOrgId: user_info.deptId,
					curActInstId: this.pListData.curActInstId,
					destActDefId: val.actDefId,
					// 当前环节ID、流程定义ID、流程实例ID
					curActDefId: this.pListData.curActDefId,
					procDefKey: this.pListData.procDefKey,
					procInstId: this.pListData.procInstId,
				};
				_this.loading = true
				getNextactuserByPending(params)
					.then((res) => {
						// 获取流程待选人员展示方式
						_this.loading = true
						selectUserStyle(params).then((selectRes) => {
							if (selectRes.data == 'tree') {
								// 树形结构展示
								_this.userList = this.handleTree(res.data, "id");
							} else {
								// 平铺结构展示
								_this.userList = res.data.filter(item => item.type === 'USER')
							}
							let defaultStaffIds = []
							if (_this.defaultStaff && _this.defaultStaff.length > 0) {
								//设定了流程默认执行人
								let item = _this.defaultStaff.find(item => item.nodeCode === val.actDefId)
								if (item && item.users) {
									defaultStaffIds = JSON.parse(item.users).map(user => user)
								}
							}
							if (defaultStaffIds.length > 0) {
								let userList = []
								_this.defaultUserList(userList, defaultStaffIds, _this.userList)
								userList.forEach(user => {
									_this.userNodeClick(user);
								})
								_this.editStatus = false
							} else {
								_this.editStatus = true
								//如果待选人员里就一个人，自动加入到已选人员名单里z
								_this.rtuserList(res.data);
								if (_this.morendta && _this.morendta.length > 0) {
									_this.morendta.forEach(user => {
										_this.userNodeClick(user);
									})
								}
							}
							// 关闭遮挡
							_this.loading = false;
						});
					})
					.catch((e) => {
						_this.loading = false;
					}).finally(()=>{
						uni.hideLoading()
					});
			},
			processNodeClick2(val) {
				let _this = this
				let user_info = this.userInfo
				if (this.nextData != "") {
					if (val.actDefName == this.nextData.actDefName) {
						uni.hideLoading()
						return;
					}
				}
				this.nextData = val;
				this.userList = []; //代选
				this.receiveUserList = []; //已选
				let params = {
					procDefId: this.pListData.procDefId,
					userOrgId: user_info.deptId,
					curActDefId: this.pListData.actDefId,
					destActDefId: val.actDefId,
				};
				_this.loading = true
				getNextActUsersByNew(params).then((res) => {
						// 获取流程待选人员展示方式
						selectUserStyle(params).then((selectRes) => {
							if (selectRes.data == 'tree') {
								// 树形结构展示
								_this.userList = this.handleTree(res.data, "id");
							} else {
								// 平铺结构展示
								_this.userList = res.data.filter(item => item.type === 'USER')
							}
							let defaultStaffIds = []
							if (_this.defaultStaff && _this.defaultStaff.length > 0) {
								//设定了流程默认执行人
								let item = _this.defaultStaff.find(item => item.nodeCode === val.actDefId)
								if (item && item.users) {
									defaultStaffIds = JSON.parse(item.users).map(user => user)
								}
							}
							if (defaultStaffIds.length > 0) {
								let userList = []
								_this.defaultUserList(userList, defaultStaffIds, _this.userList)
								userList.forEach(user => {
									this.userNodeClick(user);
								})
								_this.editStatus = false
							} else {
								_this.editStatus = true
								//如果待选人员里就一个人，就自己加入到已选人员名单里z
								this.rtuserList(res.data);
								if (_this.morendta && _this.morendta.length > 0) {
									_this.morendta.forEach(user => {
										_this.userNodeClick(user);
									})
								}
							}
							_this.loading = false
						});

					})
					.catch((e) => {
						this.loading = false;
					}).finally(()=>{
						uni.hideLoading()
					});
			},
			//点击成员 导入可选成员
			userNodeClick(val) {
				if (val.type == "USER") {
					if (this.nextData.multi) {
						// 多人处理环节
						let arr = this.receiveUserList.filter((x) => x.id === val.id);
						if (arr.length <= 0) {
							this.receiveUserList.push(val);
						}
					} else {
						// 单人处理环节
						this.receiveUserList = [val]
					}
				}
			},
			//移除成员
			removeData(item) {
				var arr = [];
				this.receiveUserList.forEach((element) => {
					if (element.id != item.id) {
						arr.push(element);
					}
				});
				this.receiveUserList = arr;
			},
			defaultUserList(userList, defaults, data) {
				defaults.forEach(item => {
					let d = {
						type: "USER",
						id: item.userName,
						name: item.nickName,
						parentId: item.deptId
					}
					userList.push(d)
					if (!data.some(v => v.id === item.userName)) {
						data.push(d)
					}
				})

			},

			rtuserList(originalData) {
				let userList = originalData.filter(item => item.type === 'USER')
				if (userList.length == 1) {
					// 若是用户节点直接设置成为已选用户
					this.morendta = userList.slice(0, 1);;
				} else {
					if (this.selected) {
						if (this.nextData.multi) {
							this.morendta = userList
						} else {
							this.morendta = userList.slice(0, 1);
						}
					} else {
						this.morendta = undefined
					}
				}
			},
			// 从组织用户结构树中寻找叶子节点，不需要的
			filterUserNode(data) {
				if (data.type == "USER") {
					// 若是用户节点就返回
					return data
				} else if (data.hasOwnProperty("children")) {
					let list = data.children;
					for (var i = 0; i < list.length; i++) {
						let item = list[i];
						if (item.type != "USER") {
							let res = this.filterUserNode(item)
							if (res != null) {
								// 返回出方法
								return res;
							}
						} else {
							// 若是用户节点就返回
							return item;
						}
					}
				}
			},
			filterNode(value, data) {
				if (!value) return true;
				return data.id.toLowerCase().indexOf(value.toLowerCase()) !== -1 || data.name.toLowerCase().indexOf(value
					.toLowerCase()) !== -1
			},
		},
	};
</script>