<template>
	<view class="ft-card">
		<view class="ctr" v-for="(item, index) in dataList" :key="index">
			<!-- 文件类型 -->
			<view class="clist">
				<view class="clum">{{$t(`myItem.borrow_file_type`)}}</view>
				<view class="text">{{ formatterDocClass(item.docClass) }}</view>
			</view>
			<!-- 文件名称 -->
			<view class="clist">
				<view class="clum">{{$t(`myItem.borrow_file_name`)}}</view>
				<view class="text">
					<u--text :text="item.docName"></u--text>
				</view>
			</view>

			<!-- 文件编号 -->
			<view class="clist">
				<view class="clum">{{$t(`myItem.borrow_file_id`)}}</view>
				<view class="text">{{ item.docId }}</view>
			</view>
			<!-- 文件版本 -->
			<view class="clist">
				<view class="clum">{{$t(`myItem.borrow_file_ver`)}}</view>
				<view class="text">{{ item.versionValue }}</view>
			</view>
		</view>
	</view>

</template>

<script>
	import {
		settingDocClassList
	} from "@/api/file_settings/type_settings";
	export default {
		name: "LinkRecord",
		props: {
			dataList: {
				type: Array,
				default: () => []
			},
		},
		data() {
			return {
				docClassList: [],
				docClassTree: [],
			}
		},
		mounted() {
			this.getDocClassList()
		},
		methods: {
			getDocClassList() {
				// 获取文件分类集合
				settingDocClassList({
					classStatus: "1",
					neClassType: 'foreign'
				}).then(res => {
					this.docClassList = JSON.parse(JSON.stringify(res.rows))
					this.docClassTree = this.handleTree(res.rows, "id", "parentClassId")
				});
			},
			formatterDocClass(cellValue) {
				let _this = this
				let item = _this.docClassList.find(item => item.id === cellValue)
				return item ? item.className : cellValue
			},
			formatter(dataList, value) {
				let item = dataList.find(item => item.value === value)
				return item ? item.label : value
			}
		},
	}
</script>