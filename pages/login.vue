<template>
	<view class="login-page">
		<image class="logo" mode="aspectFit" src="@/static/logo.png"></image>
		<!-- 欢迎登录体系文件管理系统 -->
		<view class="title">{{ $t('login.welcomeTip') }}</view>
		<u--form labelPosition="left">
			<u-form-item leftIcon="account">
				<!-- 请输入用户名称 -->
				<u--input v-model="loginForm.username" border="none" :placeholder="$t('login.username')"></u--input>
			</u-form-item>
			<u-form-item leftIcon="lock">
				<!-- 请输入密码 -->
				<u--input v-model="loginForm.password" border="none" :placeholder="$t('login.password')"
					type="password"></u--input>
			</u-form-item>
		</u--form>
		<u-button @click="handleLogin()">{{ $t('login.login') }}</u-button>
		<u-toast ref="uToast"></u-toast>

	</view>
	</view>
</template>

<script>
	import {
		getCodeImg
	} from '@/api/login'

	export default {
		data() {
			return {
				codeUrl: "",
				captchaEnabled: true,
				// 用户注册开关
				register: false,
				globalConfig: getApp().globalData.config,
				loginForm: {
					username: "",
					password: "",
					code: "",
					uuid: ''
				}
			}
		},
		created() {
			this.getCode()
		},
		methods: {
			// 用户注册
			handleUserRegister() {
				this.$tab.redirectTo(`/pages/register`)
			},
			// 隐私协议
			handlePrivacy() {
				let site = this.globalConfig.appInfo.agreements[0]
				this.$tab.navigateTo(`/pages/common/webview/index?title=${site.title}&url=${site.url}`)
			},
			// 用户协议
			handleUserAgrement() {
				let site = this.globalConfig.appInfo.agreements[1]
				this.$tab.navigateTo(`/pages/common/webview/index?title=${site.title}&url=${site.url}`)
			},
			// 获取图形验证码
			getCode() {
				getCodeImg().then(res => {
					this.captchaEnabled = res.data.captchaOnOff === undefined ? true : res.data.captchaOnOff;
					if (this.captchaEnabled) {
						this.codeUrl = 'data:image/gif;base64,' + res.data.img
						this.loginForm.uuid = res.data.uuid
					}
				})
			},
			// 登录方法
			async handleLogin() {
				if (this.loginForm.username === "") {
					// 请输入您的账号
					this.$refs.uToast.show({
						type: 'error',
						icon: false,
						message: this.$t(`login.usernameTip`),
					})
				} else if (this.loginForm.password === "") {
					// this.$modal.msgError("请输入您的密码")
					this.$refs.uToast.show({
						type: 'error',
						icon: false,
						message: this.$t(`login.passwordTip`),
					})
				} else if (this.loginForm.code === "" && this.captchaEnabled) {
					// this.$modal.msgError("请输入验证码")
					this.$refs.uToast.show({
						type: 'error',
						icon: false,
						message: this.$t(`login.codeTip`),
					})
				} else {
					// this.$modal.loading("登录中，请耐心等待...")
					this.$refs.uToast.show({
						type: 'error',
						icon: false,
						message: this.$t(`login.loadingTip`),
					})
					this.pwdLogin()
				}
			},
			// 密码登录
			async pwdLogin() {
				this.$store.dispatch('Login', this.loginForm).then(() => {
					this.$modal.closeLoading()
					this.loginSuccess()
				}).catch(() => {
					if (this.captchaEnabled) {
						this.getCode()
					}
				})
			},
			// 登录成功后，处理函数
			loginSuccess(result) {
				// 设置用户信息
				this.$store.dispatch('GetInfo').then(res => {
					this.$tab.switchTab('/pages/index')
				})
			}
		}
	}
</script>