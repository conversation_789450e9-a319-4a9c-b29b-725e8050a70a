<template>
	<view>
		<u-navbar :autoBack="true" :title="$t(`doc.this_dept_process_monitor`)" height="44" :fixed="true">
		</u-navbar>
		<view class="u-page">
			<iframe :src="flowUrl" width="100%" height="1400px;" frameborder="0"></iframe>

		</view>
	</view>
</template>

<script>
	import {
		getHistAskLogUrl
	} from "@/api/my_business/workflow.js"
	export default {
		data() {
			return {
				procInstId: null,
				flowUrl: null
			}
		},
		onLoad({
			procInstId
		}) {
			this.procInstId = procInstId
		},
		mounted() {
			this.init()
		},
		methods: {

			init() {
				getHistAskLogUrl(this.procInstId).then(res => {
					this.flowUrl = res.data
				}).catch(() => {})
			}
		}
	}
</script>

<style lang="scss">
	.toptitel {
		background-color: #ffff;
		padding: 5px;
		margin-bottom: 10px;
	}

	.rz-desc {
		color: #909399;
		font-size: 12px;
	}
</style>