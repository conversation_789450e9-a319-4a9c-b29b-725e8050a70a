<template>
	<view>
		<view class="ft-card white-card">
			<view class="ctr" v-for="(item,index) in dataList" :key="index">
				<u-form labelWidth="80">
					<u-form-item :label="$t(`doc.this_dept_link`)" align="left" prop="actDefName" width="200px">
						{{item.actDefName}}
					</u-form-item>
					<u-form-item :label="$t(`doc.this_dept_comments`)" align="left" prop="opinion">
						<span
							:style="{color:passList[item.pass].color,'margin-right':'20px'}">{{ passList[item.pass].label }}</span><br />
						<span>{{ item.opinion }}</span>
					</u-form-item>
					<u-form-item :label="$t(`doc.this_dept_operation_msg`)" align="left" prop="createTime">
						<span>{{ item.deptName }}</span>
						<span style="margin-left:10px;">{{ item.nickName }}</span>
						<span
							style="margin-left:20px;color:#AAAAAA">{{ parseTime(item.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
					</u-form-item>
				</u-form>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		shlkSelectLogByProcInstId
	} from "@/api/my_business/workflow";

	export default {
		name: "WorkflowLogs",
		props: ["procInstId"],
		data() {
			return {
				loading: true,
				dataList: [],
				passList: {
					'true': {
						label: this.$t(`doc.this_dept_pass`),
						color: '#70B603'
					},
					'false': {
						label: this.$t(`doc.this_dept_not_pass`),
						color: '#D9001B'
					}
				},
			};
		},
		watch: {
			procInstId(val) {
				if (val) {
					this.getList(val)
				}
			},
		},
		mounted() {
			if (this.procInstId) {
				this.getList(this.procInstId)
			}
		},
		methods: {
			/** 查询岗位列表 */
			getList(procInstId) {
				uni.showLoading({
					mask: true
				})
				shlkSelectLogByProcInstId(procInstId).then(response => {
					this.dataList = response.data;
					this.loading = false;
				}).finally(() => {
					uni.hideLoading()
				})
			},
		}
	};
</script>