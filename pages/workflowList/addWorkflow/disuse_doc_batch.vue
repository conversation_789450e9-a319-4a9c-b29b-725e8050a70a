<template>
	<view class="u-page">
		<page-header :procInstId="procInstId" :title="$t(`doc.batch_title_text_4`)+$t(`doc.this_dept_file`)+$t(`doc.this_dept_cancel`)">
			<u--form labelPosition="left" labelWidth="30%" :rules="rules" :model="formData" ref="form1">
				<view class="white-card">
					<view class="card-head">
						<view class="head-title">{{$t(`doc.this_dept_base_msg`)}}</view>
					</view>
					<u-form-item :label="$t(`doc.this_dept_file_types`)" borderBottom prop="docClass">
						{{docClassData.className}}
					</u-form-item>
					<u-form-item :label="$t(`doc.this_dept_staffing_depts`)" borderBottom prop="deptName">
						{{formData.deptName}}
					</u-form-item>
					<u-form-item :label="$t(`doc.this_dept_staff`)" borderBottom prop="userName">
						{{formData.nickName}}
					</u-form-item>
					<!--card-head卡片标题-->
					<view class="">
						<view class="index-list" v-if="dataList.length>0">
							<view class="list" v-for="(item,index) in dataList" :key="index">
								<view class="list-img" @click="handlePreview(item.fileId)">
									<image mode="aspectFit" src="@/static/images/index-icon6.svg"></image>
								</view>
								<view class="list-text" @click="handleUpdate(item)">
									<view class="title">{{item.docName}}</view>
									<view class="tag-group">
										<u-tag :text="item.docId" size="mini"></u-tag>
										<u-tag :text="item.versionValue" size="mini"></u-tag>
									</view>
								</view>
							</view><!--list-->

						</view><!--index-list-->
						<u-empty v-else mode="list" icon="http://cdn.uviewui.com/uview/empty/list.png">
						</u-empty>
					</view>
				</view>
				<view class="white-card" v-if="nodeShow('whether_customer_record')||formData.whetherCustomer">
					<view class="card-head">
						<view class="head-title">{{$t(`doc.customer_record`)}}</view>
					</view>
					<u-form-item :label="$t(`doc.whether_customer_records`)" borderBottom labelWidth="140"
						prop="whetherCustomer">
						<dict-tag :options="dict.type.sys_yes_no" :value="formData.whetherCustomer" />
					</u-form-item>
				</view>
				<view class="white-card" v-if="!!formData.yNTrain">
					<view class="card-head">
						<view class="head-title">{{$t(`doc.this_dept_train`)}}</view>
					</view>
					<u-form-item :label="$t(`doc.this_dept_train_or_not`)" borderBottom labelWidth="140" prop="yNTrain">
						<dict-tag :options="dict.type.sys_yes_no" :value="formData.yNTrain" />
					</u-form-item>
				</view>
			</u--form>
			<approval-box v-if="!nodeShow('top_btn_publish_file')&&workflowStatus&&approvalStatus" ref="approvalBox"
				:submitLabel="submitLabel" :selected="nodeShow('default_selected')" :order="parseInt(order)"
				:searchQuery="searchQuery" :hideNodeCode="hideNodeCode" :defaultStaff="defaultStaff"
				:pListData="pListData" :status="(nodeShow('shenhe')||nodeShow('pizhun'))"></approval-box>
			<view class="white-card" v-if="(nodeShow('shenhe')||nodeShow('pizhun'))&&workflowStatus&&!approvalStatus">
				<view class="card-head">
					<view class="head-title">{{submitLabel}}</view>
				</view>
				<u--form labelPosition="left" :model="formSubmit" :rules="rules" ref="validateForm">
					<!-- 结论 -->
					<u-form-item required :label="submitLabel+$t(`doc.this_dept_conclusion`) " prop="pass" borderBottom
						labelWidth="30%">
						<u-radio-group v-model="formSubmit.pass" @change="commentItemSelect" placement="row">
							<u-radio style="margin-right: 10px;" v-for="(item,index) in passoptions" :key="index"
								:name="item.value" :label="item.label"></u-radio>
						</u-radio-group>
					</u-form-item>
					<!-- 意见 -->
					<u-form-item required :label="submitLabel + $t(`doc.this_dept_comments`) " borderBottom
						labelWidth="30%" prop="summary" class="label-top">
						<u--textarea v-model="formSubmit.summary"
							:placeholder="$t(`doc.this_dept_insert`)+submitLabel+$t(`doc.this_dept_comments`)"></u--textarea>
					</u-form-item>
				</u--form>
			</view>
			<processcode v-if="!nodeShow('top_btn_publish_file')&&workflowStatus&&!approvalStatus" ref="prochild"
				:order="order" :searchQuery="searchQuery" :hideNodeCode="hideNodeCode" :defaultStaff="defaultStaff"
				:selected="nodeShow('default_selected')"></processcode>
			<view class="foot-btn">
				<u-row>
					<u-col span="6">
						<u-button type="primary" v-if="!nodeShow('top_btn_publish_file')&&!editStatus&&workflowStatus"
							:text="$t('doc.this_dept_annex')" @click="submitForm"></u-button>
					</u-col>
					<u-col span="6">
						<u-button type="success" :text="$t('doc.this_dept_process_monitor')"
							@click="openMonitor"></u-button>
					</u-col>
				</u-row>
			</view>
			<u-toast ref="uToast"></u-toast>
		</page-header>
	</view>
</template>

<script>
	import {
		workflowprocesskey,
		getStartActdef,
		getExtAttributeModel,
		procInstInfoAndStatus,
		workflowbacktostart,
		getRecordbyPorcInstId,
		getRedirectDefId
	} from '@/api/my_business/workflow'
	import {
		addModifyApplyBatch,
		updateModifyApply,
		getBatchInfoByBpmnId,
		trainValidateRequired
	} from "@/api/file_processing/modifiyApply";
	import {
		settingDocClassId
	} from "@/api/file_settings/type_settings";
	import {
		listPresetUser
	} from '@/api/setting/presetUser'
	import {
		modifyApplyLinklist
	} from "@/api/document_account/standard";
	import {
		listModifyApplyDistribute
	} from '@/api/document_account/extraApply';
	import {
		getInfo
	} from "@/api/setting/docClassFlowNodeDetail";
	import workflowLogs from "@/pages/common/workflowLogs/index.vue";
	import processcode from "@/pages/common/processcode/index.vue";
	import {
		settingDocClassList
	} from "@/api/file_settings/type_settings";
	import {
		checkPermi
	} from '@/utils/permission'
	import approvalBox from './add_import/approvalBox.vue';
	import {
		getLeaderByUserName
	} from '@/api/system/user'
	import PageHeader from "./add_import/pageHeader.vue";
	import {
		listDistributeGroupDetail
	} from '@/api/setting/distributeGroupDetail'
	export default {
		dicts: ['norm_standard_type', 'sys_yes_no'],
		components: {
			workflowLogs,
			processcode,
			approvalBox,
			PageHeader
		},
		data() {
			return {
				rules: {
					projectName: [{
						required: true,
						message: this.$t(`doc.this_dept_insert`) + this.$t(`doc.this_dept_project_name`),
						trigger: "blur,change",
						nodeShow: 'doc_recheck_act'
					}],
					securityClass: [{
						required: true,
						message: this.$t(`doc.this_dept_pls_select`) + this.$t(`doc.this_dept_security_class`),
						trigger: "blur,change",
						nodeShow: 'doc_recheck_act'
					}],
					keyword: [{
						required: true,
						message: this.$t(`doc.this_dept_insert`) + this.$t(`doc.this_dept_keyword`),
						trigger: "blur,change",
						nodeShow: 'doc_recheck_act'
					}],
					docBytes: [{
						required: true,
						message: this.$t(`doc.this_dept_insert`) + this.$t(`doc.this_dept_doc_bytes`),
						trigger: "blur,change",
						nodeShow: 'doc_recheck_act'
					}],
				},
				docClassData: {
					className: ''
				},
				mark: undefined,
				type: 'disuse_doc_batch',
				order: 0,
				hideNodeCode: [],
				yes: 'Y',
				no: 'N',
				classTypeForeign: 'FOREIGN',
				classTypeNote: 'NOTE',
				codeRuleDetail: [],
				backFlowToOneStatus: true,
				approvalStatus: true,
				transferStatus: false,
				drawerSize: '90%',
				form: {},
				path: 'views/workflowList/addWorkflow/',
				code: '',
				drawerShow: false,
				dataList: [],
				classTypeList: undefined,
				defaultStaff: undefined,
				classTypeRecord: 'RECORD',
				classTypeDoc: 'DOC',
				searchQuery: {},
				dealDrawerShow: false,
				submitLabel: this.$t('doc.this_dept_annex'),
				isProject: false,
				shenchenbianhao: false,
				passoptions: [{
						value: true,
						label: this.$t(`doc.this_dept_pass`)
					},
					{
						value: false,
						label: this.$t(`doc.this_dept_not_pass`)
					},
				],
				formSubmit: {
					summary: "",
					actionType: "",
					pass: ""
				},
				pButton: 'DISUSE',
				isSummary: false,
				projectList: [],
				project: {
					id: '',
					name: ''
				},
				activeName: "info",
				nodeDetail: {},
				nodeDetailList: [],
				procDefKey: undefined,
				processData: {},
				viewId: "",
				userInfo: this.$store.getters.user,
				viewShow: false,
				active: 4,
				activeIndex: "2",
				uploadType: ["doc", "docx", "ppt", "xlsx", "pdf", "jpg", "png"],
				monitorDrawerVisible: false,
				redirectDefId: undefined,
				redirectReceivers: undefined,
				formData: {
					dataType: undefined,
					docClass: undefined,
					batchId: undefined,
					changeType: undefined,
					recordStatus: undefined,
					deptId: undefined,
					deptName: undefined,
					userName: undefined,
					nickName: undefined,
					createBy: undefined,
					processStatus: undefined,
					whetherCustomer: undefined,
					yNTrain: undefined,
				},
				kuozhanshujuBool: {},
				kuozhanshuju: {},
				appendixesfileList: [],
				standardDocfileList: [],
				classLevelOptions: [],
				docClassList: [],
				summary: "",
				pListData: {},
				editStatus: false,
				workflowStatus: false,
				dialogVisible: false,
				processcodeData: {},
				processInstanceModel: {},
				disabled: false,
				mobanwenjian: [],
				loading: false,
				detailLoading: false,
				flowStepLoading: false,
				procInstId: undefined,
			}
		},
		watch: {
			"formData.docClass"(val, oldVal) {
				if (val) {
					this.getNodeDetailInfo()
				}
			},
		},
		computed: {

		},
		onLoad(option) {
			let row = option
			this.procInstId = row.procInstId
			this.workflowStatus = row.status == '1'
			this.status = row.status
			this.order = row.order
			this.mark = row.mark
			this.procInstInfoAndStatus(this.procInstId)
			this.getDetail(this.procInstId)

		},
		// onShow() {
		// 	this.getDetail()
		// },
		methods: {
			formatter(dataList, value, key, label) {
				let item = dataList.find(item => item[key] === value)
				return item ? item[label] : value
			},
			procInstInfoAndStatus(procInstId) {
				let _this = this
				procInstInfoAndStatus(procInstId).then((res) => {
					if (res) {
						_this.procDefKey = res.procDefKey
						_this.pListData = res
					} else {
						_this.pListData = {
							procInstId: procInstId
						}
					}
					_this.getExtAttributeModel()
				});
			},
			getExtAttributeModel() {
				let _this = this
				let procDefId = _this.pListData.procDefId
				let curActDefId = _this.pListData.curActDefId || _this.pListData.actDefId
				if (procDefId && curActDefId) {
					_this.getNodeDetailInfo()
					getExtAttributeModel(
						procDefId,
						curActDefId
					).then((res) => {
						let kuozhanshujuBool = {}
						let kuozhanshuju = {}
						res.data.forEach(item => {
							if (item.objType === 'Boolean') {
								kuozhanshujuBool[item.objKey] = item.objValue
							} else {
								kuozhanshuju[item.objKey] = item.objValue
							}
						})
						_this.kuozhanshujuBool = kuozhanshujuBool;
						_this.kuozhanshuju = kuozhanshuju;
					}).finally(() => {
						_this.loading = false
					});
				} else {
					_this.kuozhanshujuBool = {}
					_this.kuozhanshuju = {}
					_this.loading = false
				}
			},
			attributeModelBool(val) {
				if (this.kuozhanshujuBool && this.kuozhanshujuBool !== {}) {
					let obj = this.kuozhanshujuBool[val]
					return !!obj && obj === 'true'
				} else {
					return false
				}
			},
			attributeModel(val) {
				return this.kuozhanshuju[val]
			},
			getNodeDetailInfo() {
				let _this = this
				let curActDefId = _this.pListData.curActDefId || _this.pListData.actDefId
				if (_this.pListData && curActDefId && _this.formData.docClass) {
					getInfo(_this.formData.docClass, _this.pButton, curActDefId).then(res => {
						let nodeDetail = {}
						res.data.forEach(item => {
							nodeDetail[item.code] = true
						})
						_this.nodeDetail = nodeDetail
						_this.nodeDetailList = res.data
						_this.editStatus = _this.nodeShow('bianji') && _this.workflowStatus
						_this.submitLabel = _this.nodeShow('pizhun') ? _this.$t(`file_handle.change_approve`) :
							_this.nodeShow('shenhe') ? _this.$t(`file_handle.change_auditing`) : _this.$t(
								'doc.this_dept_annex')
						_this.jointReviewRedirect();
						_this.getWorkflowParams();
					})
				}
			},
			nodeFunCondition(code) {
				let _this = this
				let nodeDetail = _this.nodeDetailList.find(item => item.code === code)
				if (nodeDetail && nodeDetail.funCondition) {
					return JSON.parse(nodeDetail.funCondition)
				} else {
					return undefined
				}
			},
			nodeShow(code) {
				let _this = this
				if (_this.nodeDetail) {
					return !!_this.nodeDetail[code]
				} else {
					return false
				}
			},
			state(tab) {
				this.tabIndex = tab.index
			},
			getDetail(procInstId) {
				let _this = this
				_this.detailLoading = true
				getBatchInfoByBpmnId(procInstId).then(async (res) => {
					_this.dataList = res.data;
					_this.formData = res.data[0]
					let res3 = await listPresetUser({
						bizId: res.data[0].batchId
					})
					_this.formData.presetUserList = res3.data
					_this.settingDocClassId(_this.formData.docClass);
					_this.setDataList()
				}).finally(() => {
					_this.detailLoading = false
				});
			},
			setDataList() {
				let _this = this
				_this.dataList.forEach(item => {
					modifyApplyLinklist({
						applyId: item.id,
						linkType: "REF_DOC"
					}).then(res => {
						item.docLinks = res.rows;
					})
					modifyApplyLinklist({
						applyId: item.id,
						linkType: "RECORD"
					}).then(res => {
						item.recordLinks = res.rows;
					})
					modifyApplyLinklist({
						applyId: item.id,
						linkType: "NOTE"
					}).then(res => {
						item.noteLinks = res.rows;
					})
					listModifyApplyDistribute({
						applyId: item.id
					}).then(res => {
						item.distributeList = res.data
					})
				})
			},
			async getWorkflowParams() {
				let _this = this
				//会签人员
				if (_this.nodeShow('preset_countersign')) {
					let funCondition = _this.nodeFunCondition('preset_countersign')
					if (funCondition && funCondition.nodeCode && funCondition.nodeCode.length > 0 && funCondition
						.groupId) {
						let users = [];
						let res = await listDistributeGroupDetail({
							groupId: funCondition.groupId
						})
						res.rows.forEach(item => {
							users.push({
								userName: item.receiveUserName,
								nickName: item.receiveNickName,
								deptId: item.receiveUserDeptId,
								deptName: item.receiveUserDept,
							})
						})
						funCondition.nodeCode.forEach(nodeCode => {
							let preset = _this.formData.presetUserList.find(item => item.nodeCode === nodeCode)
							if (preset) {
								preset.users = JSON.stringify(users)
							} else {
								_this.formData.presetUserList.push({
									nodeCode: nodeCode,
									users: JSON.stringify(users)
								})
							}
						})
					}
				}
				//设定流程默认执行人
				let defaultStaff = []
				// 下个环节预选直属部门领导
				if (_this.nodeShow('next_set_leader')) {
					let funCondition = _this.nodeFunCondition('next_set_leader')
					if (funCondition && funCondition.nodeCode && funCondition.nodeCode.length > 0) {
						let users = undefined;
						if (funCondition.validate) {
							let res = await getLeaderByUserName(_this.formData.userName)
							if (res.data) {
								users = [{
									userName: res.data.userName,
									nickName: res.data.nickName,
									deptId: res.data.deptId,
									deptName: res.data.dept.deptName
								}]
							}
						} else {
							let leader = _this.$store.getters.leader
							if (leader) {
								users = [{
									userName: leader.userName,
									nickName: leader.nickName,
									deptId: leader.deptId,
									deptName: leader.dept.deptName
								}]
							}
						}
						if (users) {
							funCondition.nodeCode.forEach(nodeCode => {
								let preset = _this.formData.presetUserList.find(item => item.nodeCode ===
									nodeCode)
								if (preset) {
									preset.users = JSON.stringify(users)
								} else {
									_this.formData.presetUserList.push({
										nodeCode: nodeCode,
										users: JSON.stringify(users)
									})
								}
							})
						}
					}
				}
				if (_this.formData.presetUserList.length > 0) {
					defaultStaff.push(...JSON.parse(JSON.stringify(_this.formData.presetUserList)))
				}
				if (_this.nodeShow('cdxmd') && _this.formData.batch) {
					// 文件类型设置中是否设置了 需要谁驳回就再只发送给驳回的人
					let funCondition = _this.nodeFunCondition('cdxmd')
					if (funCondition && funCondition.validate) {
						//查询本次驳回有哪些人员
						let res = await listWorkflowLog({
							batch: _this.formData.batch,
							nextDefId: _this.pListData.curActDefId,
							havaDetail: true
						})
						let nodeCode = ""
						let users = []
						res.rows.forEach(item => {
							nodeCode = item.actDefId
							users.push({
								userName: item.sender,
								nickName: item.nickName,
								deptId: item.senderDeptId,
								deptName: item.deptName
							})
						})
						if (defaultStaff.length > 0) {
							let staff = defaultStaff.find(item => item.nodeCode = nodeCode)
							if (staff) {
								staff.users = JSON.stringify(users)
							}
						} else {
							defaultStaff.push({
								nodeCode: nodeCode,
								users: JSON.stringify(users)
							})
						}
					}
				}
				console.log("defaultStaff", defaultStaff)
				_this.defaultStaff = defaultStaff
				_this.searchQuery.step = _this.formData.step ? _this.formData.step : 0
				_this.searchQuery.isTrain = _this.formData.yNTrain
				_this.searchQuery.isCustomer = _this.formData.whetherCustomer
				let hideNodeCode = []
				//下一环节未预选人员隐藏
				if (_this.nodeShow('xyhjwyxryyc')) {
					let funCondition = _this.nodeFunCondition('xyhjwyxryyc')
					if (funCondition && funCondition.nodeCode) {
						let length = funCondition.nodeCode.length
						//下一环节隐藏范围 都隐藏
						hideNodeCode = funCondition.nodeCode
						//过滤有预选人员的环节
						defaultStaff.forEach(item => {
							if (item.users) {
								let users = JSON.parse(item.users)
								if (hideNodeCode.includes(item.nodeCode) && users && users.length > 0) {
									hideNodeCode = hideNodeCode.filter(code => code !== item.nodeCode)
								}
							}
						})
						//配置了反向节点 隐藏范围环节内都没预选人员 过滤掉反向节点
						if (funCondition.neNodeCode && hideNodeCode.length === length) {
							hideNodeCode = hideNodeCode.filter(code => !funCondition.neNodeCode.includes(code))
						}
						//填写了限定值 只能显示最多限定的数量
						if (funCondition.limitValue) {
							let limitValue = Number(funCondition.limitValue)
							//总数-隐藏数=显示数 显示数>限定数量
							if (!isNaN(limitValue) && (length - hideNodeCode.length) > limitValue) {
								//倒叙再插回去
								let reverse = funCondition.nodeCode.reverse()
								for (let item of reverse) {
									if (!hideNodeCode.includes(item)) {
										hideNodeCode.push(item)
									}
									if ((length - hideNodeCode.length) <= limitValue) {
										break
									}
								}
							}
						}
						// if (funCondition.validate) {
						//验证开启 配置了反向节点 隐藏范围环节内都有预选人员 增加反向节点
						if (funCondition.neNodeCode && hideNodeCode.length !== length && hideNodeCode.length ===
							0) {
							defaultStaff.forEach(item => {
								if (funCondition.neNodeCode.includes(item.nodeCode)) {
									hideNodeCode.push(item.nodeCode)
								}
							})
						}
						// }
					}
				}
				//隐藏环节列表
				_this.hideNodeCode = hideNodeCode
				_this.$nextTick(() => {
					if (_this.approvalStatus) {
						_this.$refs.approvalBox.init()
					} else {
						_this.$refs.prochild.init(_this.pListData)
					}
				})
			},
			jointReviewRedirect() {
				let _this = this
				if (_this.nodeShow('hscdx')) {
					getRecordbyPorcInstId(_this.procInstId).then(res => {
						if (res.data.length === 1) {
							let query = {
								docClass: _this.formData.docClass,
								bizType: _this.formData.changeType,
								code: "cdxmd",
								batch: _this.formData.batch,
							}
							return getRedirectDefId(query).then(res1 => {
								if (res1.data) {
									let funCondition = _this.nodeFunCondition('hscdx')
									if (funCondition && funCondition.nodeCode && funCondition.nodeCode
										.length === 1) {
										let next = res1.data.find(item => item.nextDefId === funCondition
											.nodeCode[0])
										if (next) {
											_this.redirectDefId = next.nextDefId
											_this.redirectReceivers = JSON.parse(next.receiver)
											_this.redirectOrder = next.actDefOrder
										}
									}
								}
							})
						}
					})
				}
			},
			async settingDocClassId(val) {
				let _this = this
				await settingDocClassId(val).then((response) => {
					_this.docClassData = response.data
				});
			},
			handlePreview(fileId) {
				let url = "/pages/pdfPreview/index"
				let data = {
					fileId: fileId
				}
				uni.$u.route(url, data)
			},
			handleUpdate(row) {
				this.toDetail({
					id: row.id,
					batchId: row.batchId,
					type: 'disuse_doc',
					procInstId: this.procInstId,
					status: '2'
				})
			},
			toDetail(data) {
				this.show = false
				let url = '/pages/workflowList/addWorkflow/'
				uni.$u.route(url + data.type, data)
			},
			// 审批结论选择
			commentItemSelect(val) {
				this.formSubmit.summary = this.formatter(this.passoptions, val, 'value', 'name')
			},
			async submitForm() {
				let _this = this
				_this.$refs.form1.validate().then((res) => {
					uni.showLoading({
						mask: true
					});
				}).then(async () => {
					if (!_this.procDefKey) {
						return Promise.reject(this.$t(`doc.this_dept_no_process_setting`))
					}
					let dialogVisible = true
					//审核
					if (_this.nodeShow('shenhe') || _this.nodeShow('pizhun')) {
						if (_this.approvalStatus) {
							_this.formSubmit = _this.$refs.approvalBox.formSubmit
						}
						if (_this.formSubmit.pass === undefined) {
							return Promise.reject(_this.submitLabel + _this.$t(
								`file_handle.change_result_not_null`))
						}
						if (!_this.formSubmit.pass && _this.formSubmit.summary.trim() == '') {
							return Promise.reject(_this.$t(`doc.this_dept_pls_fill`) + _this.submitLabel +
								_this.$t(`doc.this_dept_comments`))
						}
					}
					let direction = _this.pListData.actDefOrder>_this.order
					//培训记录
					if (direction&&_this.nodeShow('page_oper_add_train_record') && _this.formData.yNTrain !== _this
						.no) {
						let funCondition = _this.nodeFunCondition('page_oper_add_train_record')
						if (!funCondition || (funCondition && funCondition.validate)) {
							let res = await trainValidateRequired({
								batchId: _this.formData.batchId,
								type: 'train'
							})
							if (!res.data) {
								return Promise.reject('【' + res.msg + '】' + _this.$t(
									`doc.this_dept_pls_upload_train_file`) + '！')
							}
						}
					}
					//客户记录
					if (direction&&_this.nodeShow('add_customer_record') && _this.formData.whetherCustomer !== _this
						.no) {
						let funCondition = _this.nodeFunCondition('add_customer_record')
						if (!funCondition || (funCondition && funCondition.validate)) {
							let res = await trainValidateRequired({
								batchId: _this.formData.batchId,
								type: 'customer'
							})
							if (!res.data) {
								return Promise.reject('【' + res.msg + '】' + _this.$t(
									`sys_mgr_log.user_signature_upload_text1`) + _this.$t(
									`doc.customer_record`) + '！')
							}
						}
					}
					if (_this.nodeShow('doc_recheck_act')) {
						if (_this.dataList.some(item=>!item.projectName||!item.securityClass||!item.keyword||!item.docBytes)){
							return Promise.reject(_this.$t(`doc.basic_info_improved`))
						}
					}
					uni.hideLoading()
				}).then(() => {
					_this.$modal.confirm(_this.$t(`file_handle.submit_text`), _this.$t(
						`file_handle.change_tip`)).then(() => {
						_this.handleWorkflowSubmit()
					})
				}).catch((err) => {
					uni.hideLoading()
					if (Array.isArray(err)) {
						err = 'Form item not empty'
					}
					console.log("表单校验不成功", err)
					_this.$refs.uToast.show({
						type: 'error',
						duration: 5000,
						icon: false,
						message: err
					})
				})


			},
			async validate() {
				let _this = this
				let validate = false
				return validate;
			},
			handleWorkflowSubmit(invokeFrom) {
				let _this = this
				uni.showLoading({
					mask: true
				});
				let formData = uni.$u.deepClone(_this.formData)
				formData.dataList = this.dataList
				let wf_receivers = [];
				let wf_nextActDefId = null
				let wf_nextActDefName = null
				let direction = null
				let prochild = _this.approvalStatus ? _this.$refs.approvalBox : _this.$refs.prochild
				if (typeof(invokeFrom) == 'string' && invokeFrom == 'publish') {
					// 来源于按钮【执行发布】
					wf_nextActDefId = 'end'
					wf_nextActDefName = '结束'
					direction = true
				} else {
					if (!(prochild.nextData && prochild.nextData.actDefId)) {
						uni.hideLoading()
						return Promise.reject(this.$t(`doc.this_dept_pls_select`) + this.$t(`doc.this_dept_process_step`))
					}
					if (prochild.receiveUserList.length < 1 && prochild.nextData.actDefType !== 'endEvent') {
						uni.hideLoading()
						return Promise.reject(this.$t(`doc.this_dept_select_user_alert`))
					}
					prochild.receiveUserList.forEach((element) => {
						wf_receivers.push({
							receiveUserId: element.id,
							receiveUserOrgId: element.parentId,
						});
					});
					wf_nextActDefId = prochild.nextData.actDefId;
					wf_nextActDefName = prochild.nextData.actDefName;
					direction = _this.pListData.actDefOrder > _this.order
				}
				// 显示加载中
				_this.flowStepLoading = true
				_this.detailLoading = true
				let wf_procTitle = '【' + _this.$t(`doc.batch_title_text_1`) + '】' + _this.dataList[0].docName + _this.$t(
					`doc.batch_title_text_2`) + _this.dataList.length + _this.$t(`doc.batch_title_text_3`)
				if (_this.pListData && _this.pListData.procInstId) {
					//流程执行参数
					formData.bpmClientInputModel = {
						model: {
							wf_procDefKey: _this.procDefKey,
							wf_procDefId: _this.pListData.procDefId,
							wf_procTitle: wf_procTitle,
							wf_curActInstId: _this.pListData.curActInstId,
							wf_sendUserId: _this.userInfo.userName,
							wf_sendUserOrgId: _this.userInfo.deptId,
							wf_receivers: wf_receivers,
							wf_nextActDefId: wf_nextActDefId,
							wf_curComment: _this.formSubmit.summary,
							wf_curActDefId: _this.pListData.curActDefId,
							wf_curActDefName: _this.pListData.curActDefName,
							wf_nextActDefName: wf_nextActDefName,
						},
						order: _this.pListData.actDefOrder,
						review: _this.nodeShow('shenhe') || _this.nodeShow('pizhun'),
						applyStatus: _this.formSubmit.pass,
						type: _this.type,
						mark: _this.mark,
						direction: direction,
					};
				} else {
					//创建流程参数
					formData.bpmClientInputModel = {
						type: _this.type,
						model: {
							wf_procTitle: wf_procTitle,
							wf_nextActDefId: wf_nextActDefId,
							wf_procDefId: _this.pListData.procDefId,
							wf_procDefKey: _this.procDefKey,
							wf_sendUserId: _this.userInfo.userName,
							wf_sendUserOrgId: _this.userInfo.deptId,
							wf_receivers: wf_receivers,
							wf_curActDefName: _this.pListData.actDefName,
							wf_curActDefId: _this.pListData.actDefId,
							wf_nextActDefName: wf_nextActDefName,
						},
						order: _this.pListData.actDefOrder,
						review: _this.nodeShow('shenhe') || _this.nodeShow('pizhun'),
						applyStatus: _this.formSubmit.pass,
						mark: _this.mark,
						direction: direction,
					};
				}
				if (_this.nodeShow('log_title')) {
					let funCondition = _this.nodeFunCondition('log_title')
					if (funCondition && funCondition.limitValue) {
						formData.bpmClientInputModel.title = funCondition.limitValue
					}
				}
				if (_this.nodeShow('top_btn_publish_file') && wf_nextActDefId === 'end') {
					//办结
					formData.recordStatus = 'done'
					formData.bpmClientInputModel.jointReview = false
				} else {
					//进行中
					formData.recordStatus = 'doing'
					formData.bpmClientInputModel.jointReview = prochild.nextData.multi
				}
				if (_this.nodeShow('hscdx')) {
					formData.bpmClientInputModel.batch = formData.batch
					formData.bpmClientInputModel.redirectDefId = _this.redirectDefId
					formData.bpmClientInputModel.redirectReceivers = _this.redirectReceivers
					if (_this.redirectOrder) {
						formData.bpmClientInputModel.order = _this.redirectOrder
					}
				}
				formData.editStatus = _this.editStatus
				formData.onlyEdit = _this.nodeShow('doc_recheck_act')
				formData.presetUserEdit = _this.nodeShow('top_btn_preset_user') || _this.nodeShow('next_set_leader')
				formData.customerEdit = _this.nodeShow('whether_customer_record')
				addModifyApplyBatch(formData).then((res) => {
					if (res.code === 200) {
						_this.$modal.msgSuccess(_this.$t(`doc.this_dept_process_sub_succ`));
						_this.close()
					}
				});
			},
			openMonitor() {
				uni.$u.route('/pages/workflowList/monitor?procInstId=' + this.procInstId)
			},
			close() {
				this.$tab.switchTab("/pages/my_business/index", {
					type: 'wdbl'
				})
			}
		}
	}
</script>