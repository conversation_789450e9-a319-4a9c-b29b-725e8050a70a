<template>
	<view>
		<view class="white-card">

			<u--form labelPosition="left" :model="formData" :rules="distributeRules" ref="elFormDistribute">
				<!-- 分发 -->
				<view class="card-head">
					<view class="head-title">{{ $t(`doc.this_dept_distribute`) }}</view>
				</view>
				<!-- 是否分发 -->
				<u-form-item borderBottom labelWidth="90" :label="$t(`doc.this_dept_distribute_or_not`)"
					prop="isDistribute">
					<dict-tag :options="dict.type.sys_yes_no" :value="formData.isDistribute" />
				</u-form-item>
				<!-- 分发范围 -->
				<u-form-item borderBottom v-if="formData.isDistribute===yes" labelWidth="90"
					:label="$t(`doc.invalid_external_distribution_scope`)" prop="distributeType" required>
					<dict-tag :options="dict.type.sys_distribute_type" :value="formData.distributeType" />
				</u-form-item>
				<!-- 分发对象 -->
				<template v-if="formData.isDistribute===yes">
					<view class="ft-card"
						v-if="formData.distributeType.includes('company') &&　companyDataList.length !== 0">
						<view class="chead">
							<view class="htitle">{{$t(`doc.this_dept_distribute_to`)}}</view>
						</view>
						<view class="ctr" v-for="(row,index) in companyDataList" :key="'company'+index">
							<view class="clist">
								<view class="clum">{{$t(`file_set.type_company`)}}</view>
								<view class="text">{{row.receiveUserDept}}</view>
							</view>
							<view class="clist">
								<view class="clum">{{$t(`doc.this_print_paper`)}}</view>
								<view class="text">
								<dict-tag :options="dict.type.print_paper_type" :value="row.printPaperType" />
								</view>
							</view>
							<view class="clist">
								<view class="clum">{{$t(`doc.this_dept_print_copies`)}}</view>
								<view class="text">
									{{ row.nums}}
								</view>
							</view>
						</view>
					</view>
					<view class="ft-card" v-if="formData.distributeType.includes('dept') &&　deptDataList.length !== 0">
						<view class="chead">
							<view class="htitle">{{$t(`doc.this_dept_distribute_to`)}}</view>
						</view>
						<view class="ctr" v-for="(row,index) in deptDataList" :key="'dept'+index">
							<view class="clist">
								<view class="clum">{{$t(`doc.this_dept_dept`)}}</view>
								<view class="text">{{row.receiveUserDept}}</view>
							</view>
							<view class="clist" v-if="setDeptReceiver||!workflowStatus">
								<view class="clum">{{$t(`doc.this_dept_people`)}}</view>
								<view class="text">
									{{row.receiveNickName}}
								</view>

							</view>
							<view class="clist">
								<view class="clum">{{$t(`doc.this_print_paper`)}}</view>
								<view class="text">
								<dict-tag :options="dict.type.print_paper_type" :value="row.printPaperType" />
								</view>
							</view>
							<view class="clist">
								<view class="clum">{{$t(`doc.this_dept_print_copies`)}}</view>
								<view class="text">
									{{row.nums}}
								</view>
							</view>
						</view>

					</view>

					<view class="ft-card"
						v-if="formData.distributeType.includes('person') &&　personDataList.length !== 0">
						<view class="chead">
							<view class="htitle">{{$t(`doc.this_dept_distribute_to`)}}</view>
						</view>
						<view class="ctr" v-for="(row,index) in personDataList" :key="'person'+index">
							<view class="clist">
								<view class="clum">{{$t(`doc.this_dept_dept`)}}</view>
								<view class="text">{{row.receiveUserDept}}</view>
							</view>
							<view class="clist">
								<view class="clum">{{$t(`doc.this_dept_people`)}}</view>
								<view class="text">{{row.receiveNickName}}</view>
							</view>
							<view class="clist">
								<view class="clum">{{$t(`doc.this_print_paper`)}}</view>
								<view class="text">
								<dict-tag :options="dict.type.print_paper_type" :value="row.printPaperType" />
								</view>
							</view>
							<view class="clist">
								<view class="clum">{{$t(`doc.this_dept_print_copies`)}}</view>
								<view class="text">
									{{row.nums}}
								</view>
							</view>
						</view>
					</view>
				</template>
			</u--form>

		</view>
		<view class="white-card" v-if="!trainAlike">
			<u--form labelPosition="left" :model="formData" :rules="trainRules" ref="elFormTrain">
				<!--权限 -->
				<view class="card-head">
					<view class="head-title">{{ $t(`doc.purview`) }}</view>
				</view>
				<!-- 文件预览权限范围 -->
				<u-form-item labelWidth="140" borderBottom :label="$t(`doc.preview_range`)" prop="trainType" required>
					<dict-tag :options="dict.type.sys_distribute_type" :value="formData.trainType" />
				</u-form-item>
				<view class="ft-card">

					<template v-if="formData.trainType.includes('company') && trainCompanyDataList.length !== 0">
						<view class="chead">
							<view class="htitle">{{$t(`file_set.type_company`)}}</view>
						</view>
						<view class="ctr" v-for="(row,index) in trainCompanyDataList" :key="'company'+index">
							<view class="clist">
								<view class="clum">{{$t(`file_set.type_company`)}}</view>
								<view class="text">{{row.receiveUserDept}}</view>
							</view>
							<view class="clist">
								<view class="clum">{{$t(`doc.number_of_objects`)}}</view>
								<view class="text">
									{{ row.nums}}
								</view>
							</view>
						</view>
					</template>
					<template v-if="formData.trainType.includes('dept') && trainDeptDataList.length !== 0">
						<view class="chead">
							<view class="htitle">{{$t(`doc.this_dept_dept`)}}</view>
						</view>
						<view class="ctr" v-for="(row,index) in trainDeptDataList" :key="'dept'+index">
							<view class="clist">
								<view class="clum">{{$t(`doc.this_dept_dept`)}}</view>
								<view class="text">{{row.receiveUserDept}}</view>
							</view>
							<view class="clist">
								<view class="clum">{{$t(`doc.number_of_objects`)}}</view>
								<view class="text">
									{{ row.nums}}
								</view>
							</view>
						</view>
					</template>
					<template v-if="formData.trainType.includes('person') && trainPersonDataList.length !== 0">
						<view class="chead">
							<view class="htitle">{{$t(`file_set.type_personal`)}}</view>
						</view>
						<view class="ctr" v-for="(row,index) in trainPersonDataList" :key="'person'+index">
							<view class="clist">
								<view class="clum">{{$t(`doc.this_dept_dept`)}}</view>
								<view class="text">{{row.receiveUserDept}}</view>
							</view>
							<view class="clist">
								<view class="clum">{{$t(`doc.this_dept_people`)}}</view>
								<view class="text">
									{{ row.receiveNickName}}
								</view>
							</view>
							<view class="clist">
								<view class="clum">{{$t(`doc.number_of_objects`)}}</view>
								<view class="text">
									{{ row.nums}}
								</view>
							</view>
						</view>
					</template>

				</view>
			</u--form>

		</view>

	</view>
</template>
<script>
	import {
		getNumByDeptId,
		getNumByTenantId
	} from '@/api/system/user'
	import {
		updateModifyApplyDistribute
	} from '@/api/my_business/modifyApplyDistribute'

	export default {
		name: "DistributeBox",
		dicts: ['sys_distribute_type', 'sys_yes_no','print_paper_type'],
		props: {
			editStatus: {
				type: Boolean,
				default: false,
			},
			workflowStatus: {
				type: Boolean,
				default: false,
			},
			setDeptReceiver: {
				type: Boolean,
				default: false,
			},
			distributeList: {
				type: Array,
				default: () => []
			},
			companyList: {
				type: Array,
				default: () => []
			},
			deptList: {
				type: Array,
				default: () => []
			},
			deptOptions: {
				type: Array,
				default: () => []
			},
			trainAlike: {
				type: Boolean,
				default: false,
			}
		},
		data() {
			return {
				yes: 'Y',
				deptDataList: [],
				personDataList: [],
				companyDataList: [],
				trainDeptDataList: [],
				trainPersonDataList: [],
				trainCompanyDataList: [],
				formData: {
					trainType: '',
					distributeType: 'person',
					isDistribute: undefined,
				},
				setting: {},
				numRules: [{
					required: true,
					message: '打印份数不能为空',
					trigger: ['blur', 'change']
				}],
				distributeRules: {
					distributeType: [{
						required: true,
						message: this.$t(`doc.this_dept_pls_select`) + this.$t(
							`doc.invalid_external_distribution_scope`),
						trigger: ['blur', 'change']
					}]
				},
				trainRules: {
					trainType: [{
						required: true,
						message: this.$t(`doc.this_dept_pls_select`) + this.$t(`doc.preview_range`),
						trigger: ['blur', 'change']
					}]
				}
			}
		},
		watch: {
			distributeList(val) {
				let _this = this
				let deptDataList = []
				let personDataList = []
				let companyDataList = []
				let trainDeptDataList = []
				let trainPersonDataList = []
				let trainCompanyDataList = []
				val.forEach(item => {
					if (item.category === 'print') {
						//分发
						if (item.type === 'dept') {
							deptDataList.push(item)
						} else if (item.type === 'person') {
							personDataList.push(item)
						} else if (item.type === 'company') {
							companyDataList.push(item)
						}
					} else {
						//培训
						if (item.type === 'dept') {
							trainDeptDataList.push(item)
						} else if (item.type === 'person') {
							trainPersonDataList.push(item)
						} else if (item.type === 'company') {
							trainCompanyDataList.push(item)
						}
					}
				})
				_this.deptDataList = deptDataList
				_this.personDataList = personDataList
				_this.companyDataList = companyDataList
				_this.trainDeptDataList = trainDeptDataList
				_this.trainPersonDataList = trainPersonDataList
				_this.trainCompanyDataList = trainCompanyDataList
			}
		},
		methods: {
			distributeChange() {
				this.deptDataList = []
				this.personDataList = []
				this.companyDataList = []
			},
			trainChange() {
				this.trainDeptDataList = []
				this.trainPersonDataList = []
				this.trainCompanyDataList = []
			},
			async validateForm() {
				// 验证分发和培训表单
				let validateValid = true
				if (this.formData.isDistribute === this.yes) {
					validateValid = this.deptDataList.length > 0 || this.personDataList.length > 0 || this
						.companyDataList.length > 0;
					if (!validateValid) {
						return Promise.reject(this.$t(`doc.this_dept_pls_select`) + this.$t(
							`doc.this_dept_distribute_to`))
					}
				}
				if (!this.trainAlike) {
					validateValid = this.trainDeptDataList.length > 0 || this.trainPersonDataList.length > 0 || this
						.trainCompanyDataList.length > 0;
					if (!validateValid) {
						return Promise.reject(this.$t(`doc.this_dept_pls_select`) + this.$t(`doc.preview_target`))
					}
				}
				if (this.setDeptReceiver) {
					if (this.deptDataList.some(item => !item.receiveNickName || !item.receiveUserName)) {
						return Promise.reject(this.$t(`doc.this_dept_pls_select`) + this.$t(
							`doc.this_dept_receive_user`));
					}
				}
				return Promise.resolve(validateValid)
			},
			init(formData, setting, reset) {
				if (!reset) {
					this.setting = setting
				}
				let form = JSON.parse(JSON.stringify(formData))
				for (let item in setting) {
					let value = formData[setting[item]]
					if (value || reset) {
						form[item] = value
					}
				}
				this.formData = form
			},
			restData(nums, type, category) {
				return {
					id: undefined,
					nums: nums,
					receiveUserName: undefined,
					receiveNickName: undefined,
					receiveUserDeptId: undefined,
					receiveUserDept: undefined,
					type: type,
					category: category
				}
			},
			selectionBoxInit(label, selectedList, dataList, settings, treeList) {
				this.$refs.selectionBox.init(label, undefined, selectedList, dataList, settings, treeList)
			},
			userSearchInit(source, index, roleKey, multiple, selectList) {
				let _this = this
				_this.$nextTick(() => {
					_this.$refs.userList.init(source, index, null, roleKey, multiple, selectList)
				})
			},
		}
	}
</script>