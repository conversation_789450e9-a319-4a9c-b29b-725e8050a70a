<template>
 <el-dialog
   :title="$t(`doc.this_dept_select_person`)"
   :visible.sync="visible"
   width="80%"
   :close-on-click-modal="false"
   append-to-body>
    <div class="el-card__body">
      <el-form :model="queryParams" ref="queryForm"  v-show="showSearch" label-width="68px" :inline="true">
        <el-form-item :label="$t(`login.username`)" prop="userName">
          <el-input
            v-model.trim="queryParams.userName"
            :placeholder="$t(`doc.this_dept_insert`)"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item :label="$t(`doc.this_dept_name`)" prop="nickName">
          <el-input
            v-model.trim="queryParams.nickName"
            :placeholder="$t(`doc.this_dept_insert`)"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item :label="$t(`doc.this_dept_dept`)">
          <treeselect
            v-model.trim="queryParams.deptId"
            :options="deptOptions"
            :disable-branch-nodes="false"
            :normalizer="normalizer"
            :placeholder="$t(`doc.this_dept_select_dept`)"
            style="width: 200px"
            :searchable="true"
            size="mini"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{ $t(`doc.this_dept_search`) }}</el-button>
          <el-button icon="el-icon-refresh"  @click="resetQuery">{{ $t(`myItem.handle_reset`) }}</el-button>
          <el-button type="primary" v-if="multiple"  @click="selectHandle(multipleSelection)">{{ $t(`file_handle.change_confirm`) }}</el-button>
        </el-form-item>
      </el-form>
      <el-card class="gray-card">
        <el-table v-loading="loading" :data="dataList" max-height="500" @selection-change="handleSelectionChange">
          <el-table-column
            v-if="multiple"
            type="selection"
            :selectable="selectable"
            width="55">
          </el-table-column>
          <el-table-column :label="$t(`login.username`)"  prop="userName" />
          <el-table-column :label="$t(`doc.this_dept_name`)"  prop="nickName" />
          <el-table-column :label="$t(`doc.this_dept_dept`)"  prop="dept.deptName" />
          <el-table-column :label="$t(`doc.this_dept_operation`)" v-if="!multiple" width="100px" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="selectHandle(scope.row)"
              >{{ $t(`file_set.type_select`) }}</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <pagination
        :autoScroll="false"
        v-if="!this.roleKey"
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
 </el-dialog>
</template>

<script>
import { listUser, listUserByRoleKey } from '@/api/system/user'
import Treeselect from '@riophae/vue-treeselect'
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { listDept } from '@/api/system/dept'

export default {
  name: "UserList",
  components: { Treeselect },
  props: ["pageType"],
  data() {
    return {
      source: undefined,
      index: undefined,
      fileType: undefined,
      visible: false,
      roleKey: undefined,
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      deptOptions: [],
      multiple: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      dataList: [],
      multipleSelection: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: '',
        nickName: '',
        status:'0',
        deptId: undefined,
      },
    };
  },
  created() {
    this.getDeptList()
  },
  methods: {
    init(source,index,deptId,roleKey,multiple,selectList) {
      let _this = this
      _this.visible = true
      _this.selectList = selectList
      _this.source = source
      _this.index = index
      _this.queryParams.deptId = deptId
      _this.roleKey = roleKey
      _this.multiple = !!multiple
      _this.resetQuery()
    },
    /** 查询岗位列表 */
    getList() {
      this.loading = true
      /**
       * 如果页面是签名管理选择人员，则查询当前用户是否开启权限按钮，开启可选择所有人员，未开启智能选择自己
       */
      this.queryParams.pageType = this.pageType
      if (!this.roleKey) {
        listUser(this.queryParams).then(res=>{
          this.dataList = res.rows;
          this.total = res.total;
          this.loading = false;
        })
      }else {
        listUserByRoleKey(this.roleKey).then(res=>{
          this.dataList = res.data;
          this.loading = false;
        })
      }
    },
    getDeptList(){
      listDept({ status: 0}).then((response) => {
        this.deptOptions = this.handleTree(response.data, "deptId");
      });
    },
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children,
      };
    },
    selectHandle(data){
      this.$emit("selectHandle",this.source,this.index,data)
      this.visible = false
    },
    handleSelectionChange(val){
      this.multipleSelection = val;
    },
    selectable(row, index){
      if (this.selectList) {
        return !this.selectList.includes(row.userName)
      }else {
        return true
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.userName = ""
      this.queryParams.nickName = ""
      this.queryParams.status = "0"
      this.queryParams.deptId = undefined
      this.handleQuery();
    },
  }
};
</script>
