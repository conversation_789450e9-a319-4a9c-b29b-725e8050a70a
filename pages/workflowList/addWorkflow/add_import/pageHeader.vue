<template>
	<view>
		<u-navbar :autoBack="showBack" :leftIcon="showBack?'arrow-left':''" :title="title" height="44" :fixed="true" :right-text="languageLabel"
			@rightClick="handleShow">
		</u-navbar>
		<u-tabs class="tabs-2" :list="tabTop" @click="tabclick" lineColor="#333"
			:activeStyle="{color: '#333', transform: 'scale(1)'}"
			:inactiveStyle="{ color: '#666', transform: 'scale(1)'}">
		</u-tabs>
		<view v-show="tabIndex==0">
			<slot></slot>
		</view>
		<view v-show="tabIndex==1">
			<workflow-logs :procInstId="procInstId"></workflow-logs>
		</view>
		<u-action-sheet :actions="languageOptions" @select="languageChange" :show="showChangeLan"
			@close="showChangeLan=false"></u-action-sheet>
	</view>
</template>

<script>
	import WorkflowLogs from '../workflowLogs'
	import config from '@/config'
	export default {
		name: "PageHeader",
		components: {
			WorkflowLogs
		},
		props: ["procInstId", "title"],
		dicts: ["language_switch"],
		computed: {
			languageLabel: function() {
				let item = this.dict.type['language_switch'].find(v => this.language === v.value);
				if (item) {
					return this.dictLanguage(item)
				}
				return null;
			},
			languageOptions: function() {
				return this.dict.type['language_switch'].map((item) => {
					return {
						name: this.dictLanguage(item),
						value: item.value
					}
				})
			}
		},
		data() {
			return {
				showBack:config.showBack,
				tabIndex: 0,
				tabTop: [{

					name: this.$t(`doc.this_dept_info_content`),
				}, {
					name: this.$t(`doc.this_dept_approval_records`),
				}],
				showChangeLan: false,
				language: uni.getStorageSync('language'), // 语言类型
			}
		},

		methods: {
			handleShow() {

				this.showChangeLan = true
			},
			tabclick(tab) {
				this.tabIndex = tab.index
			},
			languageChange(item) {
				uni.setStorageSync('language', item.value)
				this.language = item.value
				uni.request({
					url: `${config.inetUrl}/international/findLangPackage?type=front`,
					method: 'get',
					header: {
						"Accept-Language": this.language,
						'tenantid': 'CAM'
					},
				}).then((res) => {
					if (res && res[1].data.resultCode != 200) {
						return
					}
					const {
						result
					} = res[1].data
					this.$nextTick(() => {
						uni.setStorageSync('languageData', JSON.stringify(result))
						uni.setStorageSync('language', this.language)
						this.$i18n.setLocaleMessage(this.language, result)
						this.$i18n.locale=this.language
						window.location.reload()
					})
				})

			},
		}
	}
</script>

<style>
</style>