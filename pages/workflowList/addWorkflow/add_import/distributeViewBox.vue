<template>
	<view class="u-page">
		<!-- 分发培训对象 -->
		<u-navbar :auto-back="true" :title="$t(`doc.this_dept_disuse_train_obj`)" height="44" :fixed="true">
		</u-navbar>
		<u--form ref="elForm" size="medium" labelPosition="left" label-width="30%">
			<view class="white-card">
				<view class="card-head">
					<!-- 申请信息 -->
					<view class="head-title">{{ $t(`doc.this_dept_distribute_to`) }}</view>
				</view>
				<view class="ft-card">
					<view v-for="(item,index) in dataList" :key="index" class="ctr" border>
						<u-form-item :label="$t(`doc.this_dept_receive_dept`)" u-form-item prop="receiveUserDept">
							{{item.receiveUserDept}}
						</u-form-item>
						<u-form-item :label="$t(`doc.this_dept_receive_user`)" u-form-item prop="receiveNickName">
							{{item.receiveNickName}}
						</u-form-item>
						<u-form-item :label="$t(`doc.this_dept_print_copies`)" u-form-item prop="nums">
							{{item.nums}}
						</u-form-item>
					</view>
				</view>
				<view class="card-head">
					<!-- 申请信息 -->
					<view class="head-title">{{ $t(`doc.this_dept_train_target`) }}</view>
				</view>
				<view class="ft-card">
					<view class="chead" v-if="trainCompanyDataList.length>0">
						<view class="htitle">{{ $t(`file_set.type_company`) }}</view>
					</view>
					<view v-if="trainCompanyDataList.length>0" class="ctr" v-for="(item,index) in trainCompanyDataList" :key="index" border>
						<u-form-item :label="$t(`file_set.type_company`)" u-form-item prop="receiveUserDept">
							{{item.receiveUserDept}}
						</u-form-item>
					</view>
					<view class="chead" v-if="trainDeptDataList.length>0">
						<view class="htitle">{{ $t(`doc.this_dept_dept`) }}</view>
					</view>
					<view v-if="trainDeptDataList.length>0" class="ctr" v-for="(item,index) in trainDeptDataList" :key="index" border>
						<u-form-item :label="$t(`doc.this_dept_dept`)" u-form-item prop="receiveUserDept">
							{{item.receiveUserDept}}
						</u-form-item>
					</view>
					<view class="chead" v-if="trainPersonDataList.length>0">
						<view class="htitle">{{ $t(`file_set.type_personal`) }}</view>
					</view>
					<view v-if="trainPersonDataList.length>0" class="ctr" v-for="(item,index) in trainPersonDataList" :key="index" border>
						<u-form-item :label="$t(`file_set.type_personal`)" u-form-item prop="receiveNickName">
							{{item.receiveNickName}}
						</u-form-item>
						<u-form-item :label="$t(`doc.this_dept_dept`)" u-form-item prop="receiveUserDept">
							{{item.receiveUserDept}}
						</u-form-item>
					</view>
				</view>
			</view>
		</u--form>
	</view>
</template>
<script>
	import {
		listDistribute,
		listPrintGroup
	} from '@/api/my_business/modifyApplyTrain.js'

	export default {
		name: "DistributeViewBox",
		data() {
			return {
				dataList: [],
				trainDataList: [{}],
				trainDeptDataList: [],
				trainPersonDataList: [],
				trainCompanyDataList: [],
				setting: {},
			}
		},
		onLoad(option) {
			this.init(option.versionId)
		},
		methods: {
			init(versionId) {
				this.getDistributeList(versionId)
				this.getPrintGroupList(versionId)
			},
			getDistributeList(versionId) {
				let _this = this
				//培训
				listDistribute({
					versionId: versionId,
					neType: 'print'
				}).then(res => {
					let trainDeptDataList = []
					let trainPersonDataList = []
					let trainCompanyDataList = []
					res.data.forEach(item => {
						if (item.type === 'dept') {
							trainDeptDataList.push(item)
						} else if (item.type === 'person') {
							trainPersonDataList.push(item)
						} else if (item.type === 'company') {
							trainCompanyDataList.push(item)
						}
					})
					_this.trainDeptDataList = trainDeptDataList
					_this.trainPersonDataList = trainPersonDataList
					_this.trainCompanyDataList = trainCompanyDataList
				})
			},
			getPrintGroupList(versionId) {
				let _this = this
				listPrintGroup({
					versionId: versionId
				}).then(res => {
					_this.dataList = res.data
				})
			}
		}
	}
</script>