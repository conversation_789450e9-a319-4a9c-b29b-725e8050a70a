<template>
	<view class="white-card">
		<view class="card-head">
			<view class="head-title">{{submitLabel}}</view>
		</view>
		<u--form labelPosition="left" :model="formSubmit" :rules="rules" ref="validateForm">
			<!-- 结论 -->
			<u-form-item required :label="submitLabel+$t(`doc.this_dept_conclusion`) " prop="pass" borderBottom
				labelWidth="30%">
				<u-radio-group v-model="passValue" @change="commentItemSelect" placement="column">
					<u-radio style="margin-right: 10px;" v-for="(item,index) in passOptions" :key="index"
						:name="item.value" :label="item.label+(item.value===passValue?passLabel:'')"></u-radio>
				</u-radio-group>
			</u-form-item>
			<!-- 意见 -->
			<u-form-item required :label="submitLabel + $t(`doc.this_dept_comments`) " borderBottom v-if="status"
				labelWidth="30%" prop="summary" class="label-top">
				<u--textarea v-model="formSubmit.summary"
					:placeholder="$t(`doc.this_dept_insert`)+submitLabel+$t(`doc.this_dept_comments`)"></u--textarea>
			</u-form-item>
		</u--form>
	</view>
</template>

<script>
import {
  workflowNextactsNew2,
  getNextactuserByPending,
  getNextActUsersByNew,
} from "@/api/my_business/workflow";
export default {
  name: "ApprovalBox",
  components: {
  },
  props:{
    status:{
      type: Boolean,
      default: false,
    },
    submitLabel: {
      type: String,
    },
    selected: {
      type: Boolean,
      default: false,
    },
    order: {
      type: Number,
      default: 0,
    },
    userListStatus: {
      type: Boolean,
      default: false,
    },
    searchQuery:{
      type: Object,
      default: {},
    },
    hideNodeCode: {
      type: Array,
      default: ()=>[],
    },
    defaultStaff: {
      type: Array,
      default: undefined,
    },
    pListData: {},
  },
  data() {
    return {
      loading: false,
      formSubmit: { summary: "", pass: undefined },
      passOptions: [],
      passValue: undefined,
      receiveUserList: [],
      userInfo: this.$store.getters.user,
      rules: {
        pass:[
          { required: true, message: this.$t(`doc.this_dept_pls_select`), trigger: "blur,change" },
        ],
      },
      nextData: {},
      userList: [],
    };
  },
  computed:{
    passLabel(){
      let _this = this
      if (_this.receiveUserList&&_this.receiveUserList.length>0){
        return "("+_this.receiveUserList.map(item=>item.name).join("、")+")"
      }
      return ""
    }
  },
  mounted() {

  },
  watch:{

  },
  methods: {
    init(){
      let _this = this
      _this.getOptionsList()
    },
    getOptionsList() {
      let _this = this
      let searchQuery = this.searchQuery
      searchQuery.procDefId = _this.pListData.procDefId
      if (!!_this.pListData.curActInstId) {
        searchQuery.curActInstId= _this.pListData.curActInstId
        // 增加参数：当前环节定义ID【用于步骤已执行标记】
        searchQuery.curActDefId = _this.pListData.curActDefId
      }else{
        searchQuery.curActDefId= _this.pListData.actDefId
      }
      // 增加参数：流程实例ID【用于步骤已执行标记】
      searchQuery.procInstId = _this.pListData.procInstId
      _this.loading = true
	  uni.showLoading({mask: true});
      workflowNextactsNew2(searchQuery)
        .then((res) => {
          _this.processList = res.data.filter(item=>!_this.hideNodeCode.includes(item.actDefId));
          let passOptions = []
          _this.processList.forEach(item=>{
            passOptions.push({
              value:item.actDefOrder,
              label:((item.actDefOrder>_this.order||item.actDefType==='endEvent')?_this.$t(`doc.this_dept_pass`):_this.$t(`doc.this_dept_not_pass`))+','+item.actDefName,
              data: item,
            })
          })
          _this.passOptions=passOptions
          // 关闭遮罩
          _this.loading = false
		  uni.hideLoading()
          // 自动识别是否默认选中第一个步骤
          if(_this.passOptions.length > 0) {
            _this.passValue = _this.passOptions[0].value
            _this.commentItemSelect(_this.passOptions[0].value)
          }
        })
        .catch((e) => {
          _this.loading = false;
		  uni.hideLoading()
        });
    },
    processNodeClick(val){
      let _this = this
      _this.pListData.actDefOrder = val.actDefOrder
      console.log(val)
      this.nextData = val;
      this.userList = []; //代选
      this.receiveUserList = []; //已选
      if ("endEvent"===val.actDefType) {
         return
      }
      if (!!_this.pListData.curActInstId) {
        _this.processNodeClick1(val);
      }else {
        _this.processNodeClick2(val);
      }
    },
    //流程节点点击触发 带出可选成员
    processNodeClick1(val) {
      let _this = this
      let params = {
        userOrgId: _this.userInfo.deptId,
        curActInstId: this.pListData.curActInstId,
        destActDefId: val.actDefId,
        // 当前环节ID、流程定义ID、流程实例ID
        curActDefId: this.pListData.curActDefId,
        procDefKey: this.pListData.procDefKey,
        procInstId: this.pListData.procInstId,
      };
      _this.loading = true
	  uni.showLoading({mask: true});
      getNextactuserByPending(params)
        .then((res) => {
            // 平铺结构展示
            _this.userList = res.data.filter(item => item.type==='USER')
            if (_this.defaultStaff&&_this.defaultStaff.length>0) {
              let item=_this.defaultStaff.find(item=>item.nodeCode===val.actDefId)
              if (item&&item.users) {
                JSON.parse(item.users).forEach(item=>{
                  let user = _this.userList.find(user=>user.id===item.userName)
                  if (!user) {
                    _this.userList.push({
                      type: 'USER',
                      id:item.userName,
                      realId: item.userName,
                      parentId: item.deptId,
                      name: item.nickName,
                    })
                  }
                })
              }
            }
            let defaultStaffIds = []
            if (_this.defaultStaff&&_this.defaultStaff.length>0) {
              //设定了流程默认执行人
              let item=_this.defaultStaff.find(item=>item.nodeCode===val.actDefId)
              if (item&&item.users) {
                defaultStaffIds = JSON.parse(item.users).map(user=>user.userName)
              }
            }
            if (defaultStaffIds.length>0) {
              let userList = []
              _this.defaultUserList(userList,defaultStaffIds,_this.userList)
              userList.forEach(user=>{
                _this.userNodeClick(user);
              })
            } else {
              //如果待选人员里就一个人，自动加入到已选人员名单里z
              this.rtuserList(res.data);
              if (this.morendta&&this.morendta.length>0) {
                this.morendta.forEach(user=>{
                  this.userNodeClick(user);
                })
              }
            }
            // 关闭遮挡
            _this.loading = false;
			uni.hideLoading()
        })
        .catch((e) => {
          _this.loading = false;
		  uni.hideLoading()
        });
    },
    processNodeClick2(val) {
      let _this = this
      let params = {
        procDefId:this.pListData.procDefId,
        userOrgId: _this.userInfo.deptId,
        curActDefId: this.pListData.actDefId,
        destActDefId: val.actDefId,
      };
      _this.loading = true
	  uni.showLoading({mask: true});
      getNextActUsersByNew(params).then((res) => {
            // 平铺结构展示
            _this.userList = res.data.filter(item => item.type==='USER')
            if (_this.defaultStaff&&_this.defaultStaff.length>0) {
              let item=_this.defaultStaff.find(item=>item.nodeCode===val.actDefId)
              if (item&&item.users) {
                JSON.parse(item.users).forEach(item=>{
                  let user = _this.userList.find(user=>user.id===item.userName)
                  if (!user) {
                    _this.userList.push({
                      type: 'USER',
                      id:item.userName,
                      realId: item.userName,
                      parentId: item.deptId,
                      name: item.nickName,
                    })
                  }
                })
              }
            }

          let defaultStaffIds = []
          if (_this.defaultStaff) {
            //设定了流程默认执行人
            let item=_this.defaultStaff.find(item=>item.nodeCode===val.actDefId)
            if (item&&item.users) {
              defaultStaffIds = JSON.parse(item.users).map(user=>user.userName)
            }
          }
          if (defaultStaffIds.length>0) {
            let userList = []
            _this.defaultUserList(userList,defaultStaffIds,_this.userList)
            userList.forEach(user=>{
              this.userNodeClick(user);
            })
          } else {
            //如果待选人员里就一个人，就自己加入到已选人员名单里z
            this.rtuserList(res.data);
            if (this.morendta&&this.morendta.length>0) {
              this.morendta.forEach(user=>{
                this.userNodeClick(user);
              })
            }
          }
          _this.loading = false
		  uni.hideLoading()
      })
        .catch((e) => {
          this.loading = false;
		  uni.hideLoading()
        });
    },
    defaultUserList(userList,ids,data) {
      data.forEach(item=>{
        if (ids.includes(item.id)) {
          userList.push(item)
        }
      })
    },
    //点击成员 导入可选成员
    userNodeClick(val) {
      // console.log("val", val);
      if (val.type == "USER") {
        if (this.nextData.multi){
          // 多人处理环节
          let arr = this.receiveUserList.filter((x) => x.id === val.id);
          if (arr.length <= 0) {
            this.receiveUserList.push(val);
          }
        }else {
          // 单人处理环节
          this.receiveUserList = [val]
        }
      }
    },
    rtuserList(originalData) {
      let userList = originalData.filter(item => item.type==='USER')
      if (userList.length == 1) {
        // 若是用户节点直接设置成为已选用户
        this.morendta = userList.slice(0,1);;
      } else {
        if (this.selected) {
          if (this.nextData.multi) {
            this.morendta = userList
          }else {
            this.morendta = userList.slice(0,1);
          }
        }else {
          this.morendta = undefined
        }
      }
    },
    // 审批结论选择
    commentItemSelect(val) {
      let _this = this
      let options = _this.passOptions.find(options=>options.value===val)
      if(val>_this.order||options.data.actDefType==='endEvent') {
        this.formSubmit= { summary: _this.status?_this.$t(`doc.this_dept_pass`):'', pass: true }
      } else {
        this.formSubmit= { summary: _this.status?this.$t(`doc.this_dept_not_pass`):'', pass: false }
      }
      _this.processNodeClick(options.data)
    },
    handleSelect() {
      let _this = this
      let selected = []
      if (_this.receiveUserList&&_this.receiveUserList.length>0) {
        _this.receiveUserList.forEach(item=>{
          selected.push({
            userName: item.id,
            nickName: item.name,
            deptId: item.parentId,
          })
        })
      }
      _this.$nextTick(()=>{
        _this.$refs.assignUsers.init(this.nextData.multi?"选择人员（多选）":'选择人员（单选）',null,null,selected,this.nextData.multi)
      })
    },
    userSelectHandle(source,index,data){
      let _this = this
      let selected = []
      data.forEach(user=>{
        selected.push({
          type: 'USER',
          id: user.userName,
          name: user.nickName,
          parentId: user.deptId,
          realId: user.userName,
        })
      })
      _this.receiveUserList = selected
    },
  },
};
</script>
