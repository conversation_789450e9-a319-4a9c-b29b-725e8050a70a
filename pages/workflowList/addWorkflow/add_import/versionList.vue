<template>
  <el-dialog
    :title="$t(`doc.this_dept_file_select`)"
    :visible.sync="visible"
    :close-on-click-modal="false"
    width="80%"
    append-to-body>
    <div class="el-card__body">
      <el-form :model="queryParams" ref="queryForm"  v-show="showSearch" label-width="68px" :inline="true">
        <el-form-item :label="$t(`myItem.borrow_file_type`)" prop="docClass">
          <treeselect
            style="width: 200px"
            v-model.trim="queryParams.docClass"
            :options="docClassTree"
            @select="handleSelectNode"
            @input="handleInputNode"
            :normalizer="normalizer"
            :show-count="true"
            :searchable="false"
            :placeholder="$t(`doc.this_dept_select_type`)"
          />
        </el-form-item>
        <el-form-item :label="$t(`myItem.borrow_file_name`)" prop="docName">
          <el-input
            v-model.trim="queryParams.docName"
            :placeholder="$t(`doc.this_dept_insert`)"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item :label="$t(`myItem.borrow_file_id`)" prop="docId">
          <el-input
            v-model.trim="queryParams.docId"
            :placeholder="$t(`doc.this_dept_insert`)"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{ $t(`doc.this_dept_search`) }}</el-button>
          <el-button icon="el-icon-refresh"  @click="resetQuery">{{ $t(`myItem.handle_reset`) }}</el-button>
        </el-form-item>
      </el-form>
      <el-card class="gray-card">
        <el-table
          v-loading="loading"
          :data="dataList"
          border
          @selection-change="handleSelectionChange"
          ref="multipleTable"
          header-align="left"
        >
          <el-table-column
            v-if="multiple"
            type="selection"
            width="55"
            align="left"
            :selectable="selectable"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t(`myItem.borrow_file_type`)"
            align="left"
            prop="docClass"
            :formatter="formatterDocClass"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t(`myItem.borrow_file_name`)"
            align="left"
            width="200"
            prop="docName"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.docName }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t(`myItem.borrow_file_id`)"
            align="left"
            prop="docId"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t(`myItem.borrow_file_ver`)"
            align="left"
            prop="versionValue"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t(`doc.this_dept_release_time`)"
            align="left"
            prop="releaseTime"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.releaseTime, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t(`doc.this_dept_operation`)" v-if="!multiple" width="100px" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="selectHandle(scope.row)"
              >{{ $t(`file_set.type_select`) }}</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <div slot="footer" v-if="multiple">
      <el-button @click="visible = false">{{ $t(`file_set.type_cancel`) }}</el-button>
      <el-button type="primary" @click="selectHandle(selectInfoData)">{{ $t(`file_set.type_confim`) }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { listVersion } from "@/api/document_account/version";
import { settingDocClassList } from "@/api/file_settings/type_settings";
import Treeselect from '@riophae/vue-treeselect'
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
export default {
  name: "VersionList",
  components: { Treeselect },
  props: {
    versionData: {
      type: Array,
      default: ()=>[],
    },
    dataType:{
      type: String,
      default: 'stdd'
    },
    classType: {
      type: String,
    }
  },
  data() {
    return {
      visible: false,
      source: undefined,
      index: undefined,
      multiple: false,
      docClassList:[],
      docClassIdList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      dataList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      boxClass: false,
      classTypeRecord: 'RECORD',
      classTypeDoc: 'DOC',
      classTypeForeign: 'FOREIGN',
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        docId: null,
        docClass: null,
        docName: null,
        status: "1",
        dataType: null,
        docClassList: undefined,
      },
      docClassTree: [],
      // 表单参数
      form: {},
      taskData: [], // 任务数据
      borrowOpen: false,
      addBorrow: false,
      drawerDetails: false,
      taskFormData: {},
      selectDoc: "",
      // 文件分类
      linkTypeTab: [],
      addDrawer: false,
      selectInfoData: [],
      deptOptions: [],
      pListData: {},
      userInfo: this.$store.getters.user,
    };
  },
  methods: {
    //来源、序号、是否多选
    init(source,index,multiple,type){
      this.visible = true
      this.source = source
      this.index = index
      this.multiple= multiple
      this.getDocClassList(type)
    },
    getDocClassList(type){
      // 获取文件分类集合
      if (!!this.dataType) {
        let query = {classStatus: "1", dataType:this.dataType,classType:this.classType,openPurview:true}
        if (this.classType===this.classTypeForeign){
          query.neClassType = undefined
        }else {
          query.neClassType = this.classTypeForeign
        }
        if (type) {
          query.isFileType = type
        }
         settingDocClassList(query).then(res => {
          this.docClassList = JSON.parse(JSON.stringify(res.rows))
          let data = res.rows.filter(item=>item.purview)
          this.docClassIdList = data.map(item=>item.id)
          this.docClassTree = this.handleTree(JSON.parse(JSON.stringify(data)), "id", "parentClassId")
          this.handleInputNode();
          this.getList();
        });
      }
    },
    formatterDocClass(row, column, cellValue, index){
      let _this = this
      if (_this.docClassList) {
        let item = _this.docClassList.find(item=>item.id===cellValue)
        return item?item.className:cellValue
      }
     return cellValue
    },
    /** 查询列表 */
    getList() {
      let self = this
      self.queryParams.status = "1";
      self.loading = true;
      self.queryParams.dataType = self.dataType;
      self.queryParams.classType = self.classType;
      listVersion(self.queryParams).then((response) => {
        self.dataList = response.rows;
        self.total = response.total;
        self.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleInputNode();
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selectInfoData = selection;
    },
    selectable(row, index){
      if (this.versionData) {
        let doc=this.versionData.find(item=>item.docId===row.docId)
        return !doc
      }else {
        return true
      }
    },
    selectHandle(data){
      this.$emit("selectHandle", this.source, this.index, data)
      this.visible = false
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.className,
        children: node.children,
      };
    },
    handleSelectNode(node){
      let docClassList = []
      this.getChildrenList(docClassList,node,'id',)
      this.queryParams.docClassList = docClassList
    },
    handleInputNode(value, instanceId){
      if (!value) {
        this.queryParams.docClassList = this.docClassIdList
      }
    },
    getChildrenList(docClassList,node,key){
      if (node.children && node.children.length) {
        node.children.forEach(item=>{
          this.getChildrenList(docClassList,item,key);
        })
      }else {
        docClassList.push(node[key])
      }
    },
  },
};
</script>
