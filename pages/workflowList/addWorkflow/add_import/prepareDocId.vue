<template>
  <el-dialog
    :title="$t(`doc.this_dept_select_pre_code`)"
    :visible.sync="visible"
    width="40%"
    :close-on-click-modal="false"
    append-to-body>
    <div class="el-card__body">
      <el-form :model="queryParams" ref="queryForm"  v-show="showSearch" label-width="68px" :inline="true">
        <el-form-item :label="$t(`myItem.borrow_file_id`)" prop="docId">
          <el-input
            v-model.trim="queryParams.docId"
            :placeholder="$t(`doc.this_dept_insert`)"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{ $t(`doc.this_dept_search`) }}</el-button>
          <el-button icon="el-icon-refresh"  @click="resetQuery">{{ $t(`myItem.handle_reset`) }}</el-button>
        </el-form-item>
      </el-form>
      <el-card class="gray-card">
        <el-table v-loading="loading" :data="dataList" max-height="500">
          <el-table-column :label="$t(`doc.this_dept_doc_code_type`)" align="center" prop="codeType">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.doc_code_type" :value="scope.row.codeType"/>
            </template>
          </el-table-column>
          <el-table-column :label="$t(`doc.this_dept_file_code`)"  prop="docId" />
          <el-table-column :label="$t(`doc.this_dept_operation`)" width="100px" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                size="mini"
                v-if="!selectedList.includes(scope.row.docId)"
                type="text"
                @click="selectHandle(scope.row)"
              >{{ $t(`file_set.type_select`) }}</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
  </el-dialog>
</template>

<script>
import { listPrepareId } from '@/api/setting/prepareId'

export default {
  name: "PrepareDocId",
  dicts: ['doc_code_type'],
  props: [{
  }],
  data() {
    return {
      source: undefined,
      index: undefined,
      fileType: undefined,
      visible: false,
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      dataList: [],
      selectedList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        docId: undefined,
        codeType: undefined,
        docClass: undefined,
        parentDocId: undefined,
        useStatus: '0',
      },
    };
  },
  methods: {
    //来源标识，来源序号
    init(source,index,userName,dataType,codeType,docClass,parentDocId,selectedList,oldDocId) {
      let _this = this
      _this.visible = true
      _this.selectedList=selectedList?selectedList: []
      _this.source = source
      _this.index = index
      _this.queryParams.applyBy=userName
      _this.queryParams.oldDocId=oldDocId
      _this.queryParams.dataType=dataType
      _this.queryParams.codeType=codeType
      _this.queryParams.docClass=docClass
      _this.queryParams.parentDocId=parentDocId
      if (_this.selectedList&&_this.selectedList.length>0) {
        _this.queryParams.useStatus= undefined
      }
      _this.resetQuery()
    },
    /** 查询岗位列表 */
    getList() {
      this.loading = true
      listPrepareId(this.queryParams).then(res=>{
        this.dataList = res.rows;
        this.total = res.total;
        this.loading = false;
      })
    },
    selectHandle(data){
      this.$emit("selectHandle",this.source,this.index,data)
      this.visible = false
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
  }
};
</script>
