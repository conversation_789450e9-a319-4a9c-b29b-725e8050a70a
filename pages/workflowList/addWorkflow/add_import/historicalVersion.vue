<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
      class="top"
    >
      <el-row type="flex">
        <el-col :span="12">
          <!-- <el-form-item label="" prop="postCode">
            <el-input
              v-model.trim="queryParams.postCode"
              placeholder="输入关键字搜索"
              clearable
              size="small"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh"  @click="resetQuery"
              >重置</el-button
            >
          </el-form-item> -->
        </el-col>
        <el-col :span="12" style="text-align: right">
          <!-- <el-button plain @click="handleAdd()">新增</el-button> -->
          <!-- <el-button plain>修订</el-button>
          <el-button plain>作废</el-button> -->
          <!-- <el-button icon="el-icon-more" plain></el-button> -->
        </el-col>
      </el-row>
    </el-form>
    <el-table
      v-loading="loading"
      :data="postList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t(`doc.this_dept_change_type`)" align="left" prop="changeType" :formatter="formatterChangeType"/>
      <el-table-column :label="$t(`doc.this_dept_ver`)" align="left" prop="versionValue" />
      <el-table-column :label="$t(`doc.this_dept_change_source`)" align="left" prop="preChangeCode">
        <template slot-scope="scope">
          <div class="link-box bzlink-box">
            <span style="color: #385bb4; cursor: pointer" @click="onClick(scope.row)">{{ scope.row.invokeId }}</span>
          </div>
        </template>
      </el-table-column>>
      <el-table-column :label="$t(`doc.this_dept_change_reason`)" align="left" prop="changeReason" :show-overflow-tooltip="true"/>
      <el-table-column :label="$t(`doc.this_dept_changes`)" align="left" prop="content" :show-overflow-tooltip="true"/>
      <el-table-column :label="$t(`doc.this_dept_effective_date`)" align="left" prop="startDate">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.startDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t(`doc.this_dept_release_date`)" align="left" prop="releaseTime">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.releaseTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
    </el-table>

<!--    <pagination-->
<!--      v-show="total > 0"-->
<!--      :total="total"-->
<!--      :page.sync="queryParams.pageNum"-->
<!--      :limit.sync="queryParams.pageSize"-->
<!--      @pagination="getList"-->
<!--    />-->


  </div>
</template>

<script>
import {
  listPost,
  getPost,
  delPost,
  addPost,
  updatePost,
} from "@/api/system/post";

export default {
  name: "Post",
  dicts: ["business_status"],
  props: ["detatailsData"],
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        postCode: undefined,
        postName: undefined,
        status: undefined,
      },
      // 表单参数
      form: {},
    };
  },
  watch:{
    detatailsData(val){
      if (val) {
        this.getList();
      }
    }
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询 */
    getList() {
      this.loading = true;
      //console.log("detatailsData111", this.detatailsData);
      if (this.detatailsData != undefined) {
        if (this.detatailsData.versions != null) {
          this.postList = this.detatailsData.versions;
          this.total = this.detatailsData.versions.length;
        }
      }
       this.loading = false;
    },
    onClick(row){
      this.$emit("handleClick",row)
    },
    formatterChangeType(row, column, cellValue, index){
      let _this = this
      if (cellValue) {
        let item = _this.dict.type.business_status.find(item => item.value === cellValue)
        return item?item.label:""
      }else {
        return _this.$t(`doc.this_dept_import`)
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        postId: undefined,
        postCode: undefined,
        postName: undefined,
        postSort: 0,
        status: "0",
        remark: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.postId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t(`doc.this_dept_new_add`);
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const postId = row.postId || this.ids;
      getPost(postId).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = this.$t(`sys_mgr.post_edit`);
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.postId != undefined) {
            updatePost(this.form).then((response) => {
              this.$modal.msgSuccess(this.$t(`file_set.version_edit_succ`));
              this.open = false;
              this.getList();
            });
          } else {
            addPost(this.form).then((response) => {
              this.$modal.msgSuccess(this.$t(`file_set.number_field`));
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const postIds = row.postId || this.ids;
      this.$modal
        .confirm(this.$t(`sys_mgr.post_text`) + postIds + this.$t(`file_set.signature_text1`))
        .then(function () {
          return delPost(postIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess(this.$t(`file_set.signature_delete_succ`));
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/post/export",
        {
          ...this.queryParams,
        },
        `post_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
<style lang="scss" scoped>
.top {
  margin-bottom: 10px;
  .switch {
    color: #409eff;
  }
}
</style>
