<template>
	<view class="u-page">
		<!-- :bgColor="bgColor"  -->
		<!-- 文件增发 -->
		<page-header :procInstId="procInstId" :title="$t(`doc.this_dept_file_add`)">
			<view>

				<u--form labelPosition="left" labelWidth="30%" :model="formData" :rules="rules" ref="form1">
					<view class="white-card">
						<view class="card-head">
							<!-- 申请信息 -->
							<view class="head-title">{{ $t(`doc.this_dept_appli_info`) }}</view>
						</view>
						<!-- 标题 -->
						<u-form-item :label="$t(`doc.this_dept_title`)" borderBottom prop="applyTitle" required>
							{{formData.applyTitle}}
						</u-form-item>
						<!-- 申请部门 -->
						<u-form-item :label="$t(`doc.this_dept_appli_dept`)" prop="deptName" borderBottom>
							{{formData.deptName}}
						</u-form-item>
						<!-- 申请人 -->
						<u-form-item :label="$t(`doc.this_dept_claimant`)" prop="nickName" borderBottom>
							{{formData.nickName}}
						</u-form-item>
						<!-- 申请时间 -->
						<u-form-item :label="$t(`doc.this_dept_appli_date`)" prop="applyTime" borderBottom>
							{{formData.applyTime}}
						</u-form-item>
						<!-- 申请原因 -->
						<u-form-item :label="$t(`doc.this_dept_appli_reason`)" prop="reason" borderBottom required>
							{{formData.reason}}
						</u-form-item>
					</view>
					<view class="white-card">
						<view class="card-head">
							<view class="head-title">{{ $t(`doc.this_dept_file_apply_file`) }}</view>
						</view><!--card-head卡片标题-->
						<view class="ft-card">
							<view class="ctr" v-for="(item,index) in formData.itemList" :key="index">
								<!-- 文件类型 -->
								<u-form-item :label="$t(`doc.this_dept_file_type`)" borderBottom>
									{{formatterDocClass(item.docClass)}}
								</u-form-item>
								<!-- 文件名称 -->
								<u-form-item :label="$t(`doc.this_dept_file_name`)" borderBottom>
									{{item.docName}}
								</u-form-item>
								<!-- 文件编号 -->
								<u-form-item :label="$t(`doc.this_dept_file_code`)" borderBottom>
									{{item.docId}}
								</u-form-item>
								<!-- 文件版本 -->
								<u-form-item :label="$t(`doc.this_dept_file_versions2`)" borderBottom>
									{{item.versionValue}}
								</u-form-item>
								<!-- 分发培训对象 -->
								<u-form-item :label="$t(`doc.this_dept_disuse_train_obj`)" borderBottom>
									<u-button @click="toPreview(item)" type="primary"
										:text="$t(`doc.this_dept_view`)"></u-button>
								</u-form-item>
							</view>
						</view><!--ft-card-->
					</view><!--white-card 白色卡片-->
					<distribute-box :editStatus="editStatus" :workflowStatus="workflowStatus"
						:setDeptReceiver="attributeModel('set_dept_receiver')" :deptList="deptList"
						:deptOptions="deptOptions" :companyList="companyList" :distributeList="distributeList"
						:trainAlike="true" ref="distributeBox"></distribute-box>
				</u--form>
				<approval-box v-if="workflowStatus&&approvalStatus" ref="approvalBox"
								:submitLabel="submitLabel" :selected="attributeModel('default_selected')"
							    :order="parseInt(order)" :searchQuery="searchQuery"
							    :defaultStaff="defaultStaff" :pListData="pListData"
								:status="(attributeModel('shenhe')||attributeModel('pizhun'))"></approval-box>
				<view class="white-card" v-if="(attributeModel('shenhe')||attributeModel('pizhun'))&&workflowStatus&&!approvalStatus">
					<view class="card-head">
						<view class="head-title">{{submitLabel}}</view>
					</view>
					<u--form labelPosition="left" labelWidth='100' :model="formSubmit" :rules="rules"
						ref="validateForm">
						<!-- 结论 -->
						<u-form-item required :label="submitLabel+$t(`doc.this_dept_conclusion`) " prop="pass"
							borderBottom>
							<u-radio-group v-model="formSubmit.pass" @change="commentItemSelect" placement="row">
								<u-radio style="margin-right: 10px;" v-for="(item,index) in passoptions" :key="index"
									:name="item.value" :label="item.label"></u-radio>
							</u-radio-group>
						</u-form-item>
						<!-- 意见 -->
						<u-form-item required :label="submitLabel + $t(`doc.this_dept_comments`) " borderBottom
							class="label-top">
							<u--textarea v-model="formSubmit.summary"
								:placeholder="$t(`doc.this_dept_insert`)+submitLabel+$t(`doc.this_dept_comments`)"></u--textarea>
						</u-form-item>
					</u--form>
				</view>

				<processcode v-if="workflowStatus&&!approvalStatus" ref="prochild" :searchQuery="searchQuery" :order = "order"
					:defaultStaff="defaultStaff" :selected="attributeModel('default_selected')"></processcode>
				<view class="foot-btn">
					<u-row>
						<u-col span="6">
							<u-button type="primary" v-if="!editStatus&&workflowStatus"
								:text="$t('doc.this_dept_annex')" @click="submitForm"></u-button>
						</u-col>
						<u-col span="6">
							<u-button type="success" :text="$t('doc.this_dept_process_monitor')"
								@click="openMonitor"></u-button>
						</u-col>
					</u-row>
				</view><!--foot-btn 固定底部按钮-->
				<u-toast ref="uToast"></u-toast>
			</view>
		</page-header>
	</view>

</template>
<script>
	import {
		settingDocClassList
	} from "@/api/file_settings/type_settings";
	import {
		settingDocClassId
	} from "@/api/file_settings/type_settings";
	import {
		isExistByName
	} from "@/api/document_account/standard";
	import {
		workflowprocesskey,
		getStartActdef,
		getExtAttributeModel,
		procInstInfoAndStatus,
	} from '@/api/my_business/workflow'
	// PDF本地文件预览
	import {
		getWorkflowApplyLog
	} from '@/api/my_business/workflowApplyLog'
	import {
		addGroupReviewApply,
		addReviewApply,
		getReviewApplyByBpmnId,
		updateReviewApply
	} from '@/api/document_account/reviewApply'
	import {
		listDept
	} from "@/api/system/dept";
	import {
		getCompanyList
	} from "@/api/system/user";
	import DistributeBox from './add_import/distributeBox.vue'
	import {
		addExtraApply,
		getInfoByBpmnId
	} from "@/api/file_processing/modifiyApply";
	import {
		addExtraApplyByBpmnId,
		listExtraApplyItem
	} from "@/api/my_business/modifyApplyTrain";
	import {
		listModifyApplyDistribute
	} from "@/api/my_business/modifyApplyDistribute";
	import {
		getInfo
	} from "@/api/setting/docClassFlowNodeDetail";
	import processcode from "@/pages/common/processcode/index.vue";
	import PageHeader from "./add_import/pageHeader.vue";
	import approvalBox from './add_import/approvalBox.vue';
	export default {
		name: "Extra_doc",
		components: {
			processcode,
			PageHeader,
			DistributeBox,
			approvalBox
		},
		data() {

			return {
				approvalStatus: true,
				order: 0,
				open: false,
				defaultStaff: undefined,
				searchQuery: {},
				nodeDetailList: [],
				passoptions: [{
						value: true,
						label: this.$t(`doc.this_dept_pass`)
					},
					{
						value: false,
						label: this.$t(`doc.this_dept_not_pass`)
					},
				],
				docClassList: [],
				appendixsList: [],
				remarkfileList: [],
				changeFactor: [],
				actionTypeoptions: [{
						value: "keep",
						label: this.$t(`doc.this_dept_file_status_quo`)
					},
					{
						value: "update",
						label: this.$t(`file_set.type_revise`)
					},
					{
						value: "disuse",
						label: this.$t(`file_set.type_repeal`)
					},
				],
				submitLabel: this.$t('doc.this_dept_annex'),
				isProject: false,
				docIdData: {},
				jiluliData: [],
				shenchenbianhao: false,
				formSubmit: {
					summary: "",
					actionType: "",
					pass: undefined
				},
				pButton: undefined,
				isSummary: false,
				projectList: [],
				project: {
					id: '',
					name: ''
				},
				activeName: "info",
				nodeDetail: [],
				procDefKey: undefined,
				processData: {},
				viewId: "",
				userInfo: this.$store.getters.user,
				viewShow: false,
				active: 4,
				activeIndex: "1",
				uploadType: ["doc", "docx", "ppt", "xlsx", "pdf", "jpg", "png"],
				monitorDrawerVisible: false,
				formData: {
					id: undefined,
					applyTitle: undefined,
					deptId: undefined,
					userName: undefined,
					reason: undefined,
					status: undefined,
					createTime: undefined,
					itemList: []
				},
				kuozhanshuju: {},
				field117Action: "",
				action: "/dms-admin/process/file/local_upload",
				appendixesfileList: [],
				standardDocfileList: [],
				menuitem: "1",
				summary: "",
				pListData: {},
				editStatus: false,
				workflowStatus: false,
				dialogVisible: false,
				processcodeData: {},
				processInstanceModel: {},
				disabled: false,
				deptOptions: [],
				deptList: [],
				companyList: [],
				distributeList: [],
				loading: true,
				detailLoading: true,
				flowStepLoading: false,

				classTypeForeign: 'FOREIGN',
				rules: {
					isTrain: [{
						required: true,
						message: this.$t(`doc.this_dept_select_is_train`),
						trigger: "blur,change"
					}, ],
					isDistribute: [{
						required: true,
						message: this.$t(`doc.this_dept_select_is_distribute`),
						trigger: "blur,change"
					}, ],
					isPuttingOut: [{
						required: true,
						message: this.$t(`doc.this_dept_select_whether_outgoing`),
						trigger: "blur"
					}, ],
					applyTitle: [{
						required: true,
						message: this.$t(`doc.this_dept_insert_title`),
						trigger: "blur"
					}, ],
					pass: [{
						required: true,
						message: this.$t(`doc.this_dept_pls_select`),
						trigger: "blur"
					}, ],
					reason: [{
						required: true,
						message: this.$t(`doc.this_dept_insert_appli_reason`),
						trigger: "blur"
					}, ],
				},
			};
		},


		async onLoad(option) {
			let row = option
			this.procInstId = row.procInstId
			this.workflowStatus = row.status == '1'
			this.status = row.status
			this.type = row.type
			if (row.order) {
				this.order = row.order
			}
			this.formData.type = row.type
			if (row.preChangeCode) {
				let res = await getWorkflowApplyLog(row.preChangeCode)
				this.procInstId = res.data.procInstId
				this.procInstInfoAndStatus(this.procInstId)
				this.getDetail(this.procInstId)
			} else {
				this.procInstInfoAndStatus(this.procInstId)
				this.getDetail(this.procInstId)
			}
		},
		methods: {
			commentItemSelect(val) {
				this.formSubmit.summary =this.passoptions.find(v=>v.value == val).label
				this.searchQuery.pass = this.formSubmit.pass
				this.$refs.prochild.init(this.pListData)
			},
			procInstInfoAndStatus(procInstId) {
				let _this = this
				uni.showLoading({
					mask: true
				})
				procInstInfoAndStatus(procInstId).then((res) => {
					if (res) {
						_this.procDefKey = res.procDefKey
						_this.pListData = res
					} else {
						_this.pListData = {
							procInstId: procInstId
						}
					}
					_this.getExtAttributeModel()
					if (this.$refs.prochild) {
						this.$refs.prochild.init(_this.pListData)
					}
					uni.hideLoading()
				});
			},
			getModifyApplyDistributeList(applyId) {
				let _this = this
				listModifyApplyDistribute({
					applyId: applyId
				}).then(res => {
					_this.distributeList = res.data
					if (_this.$refs.distributeBox) {
						_this.$refs.distributeBox.init(_this.formData, {
							trainType: 'trainType',
							distributeType: 'distributeType',
							isDistribute: 'yNDistribute'
						})
					}
				})
			},
			// 处理查看分发培训对象
			toPreview(row) {
				uni.$u.route('/pages/workflowList/addWorkflow/add_import/distributeViewBox', row)
			},
			getDeptList() {
				// deptLevel = 2 只显示组织层级2级以内的节点
				listDept({
					status: 0
				}).then((response) => {
					this.deptList = JSON.parse(JSON.stringify(response.data))
					this.deptOptions = this.handleTree(response.data, "deptId");
				});
			},
			getCompanyDataList() {
				let _this = this
				getCompanyList({}).then(res => {
					_this.companyList = res.data
				})
			},
			formatterPassOptions(value) {
				let _this = this
				if (value) {
					let option = _this.actionTypeoptions.find(item => item.value === value)
					return option ? option.label : value
				}
				return ""
			},
			getDetail(procInstId) {
				let _this = this
				_this.detailLoading = true
				addExtraApplyByBpmnId(procInstId).then(async (res) => {
					let formData = res.data;
					formData.type = _this.formData.type
					_this.formData = formData
					_this.getSettingDocClassTreeseList();
					_this.getItemList(formData.id)
					_this.getModifyApplyDistributeList(formData.id)
				}).finally(() => {
					_this.detailLoading = false
				});
			},
			getItemList(applyId) {
				listExtraApplyItem({
					applyId: applyId
				}).then(res => {
					this.$set(this.formData, 'itemList', res.data)
				})
			},
			nodeShow(code) {
				let _this = this
				if (_this.nodeDetail) {
					return !!_this.nodeDetail.find(node => node.code === code)
				} else {
					return false
				}
			},
			getExtAttributeModel() {
				let _this = this
				let procDefId = _this.pListData.procDefId
				let curActDefId = _this.pListData.curActDefId || _this.pListData.actDefId
				if (procDefId && curActDefId) {
					getExtAttributeModel(
						procDefId,
						curActDefId
					).then((res) => {
						console.log("扩展属性====>", res);
						let kuozhanshuju = {}
						res.data.forEach(item => {
							kuozhanshuju[item.objKey] = item.objValue
						})
						_this.kuozhanshuju = kuozhanshuju;
					}).finally(() => {
						_this.loading = false
						_this.initStatus()
					});
				} else {
					_this.kuozhanshuju = {}
					_this.loading = false
				}
			},
			async initStatus() {
			  let _this = this
				_this.editStatus = _this.attributeModel('bianji') && _this.workflowStatus
				_this.submitLabel = _this.attributeModel('pizhun') ? _this.$t(`file_handle.change_approve`) : _this.attributeModel('shenhe')?_this.$t(`file_handle.change_auditing`):_this.$t('doc.this_dept_annex')
				_this.$nextTick(() => {
					if (_this.approvalStatus) {
						_this.$refs.approvalBox.init()
					} else {
						_this.$refs.prochild.init(_this.pListData)
					}
				})
			},
			attributeModel(val) {
				if (this.kuozhanshuju && this.kuozhanshuju !== {}) {
					let obj = this.kuozhanshuju[val]
					return obj ? obj === 'true' : false
				} else {
					return false
				}
			},
			handlePreview(fileId) {
				let url = `/pages/pdfPreview/index?fileId=${fileId}`
				uni.$u.route(url)
			},
			// 提交
			async submitForm() {
				this.$refs.form1.validate().then(() => {
					uni.showLoading({
						mask: true
					});
					if (this.$refs.distributeBox) {
						return this.$refs.distributeBox.validateForm()
					}
				}).then(async () => {
					let _this = this
					// 首先页签调整为 信息内容
					_this.activeName = 'info'
					if (!_this.procDefKey) {
						return Promise.reject(this.$t(`doc.this_dept_no_process_setting`))
					}
					//审核
					if (_this.attributeModel('shenhe') || _this.attributeModel('pizhun')) {
						if (_this.approvalStatus) {
							_this.formSubmit = _this.$refs.approvalBox.formSubmit
						}
						if (_this.formSubmit.pass===undefined) {
							return Promise.reject(_this.submitLabel + _this.$t(`file_handle.change_result_not_null`))
						}
						if (!_this.formSubmit.pass&&_this.formSubmit.summary.trim() == '') {
							return Promise.reject(_this.$t(`doc.this_dept_pls_fill`)+_this.submitLabel+_this.$t(`doc.this_dept_comments`))
						}
					}
					let formData = JSON.parse(JSON.stringify(_this.formData))
					let wf_receivers = [];
					let wf_nextActDefId = null
					let wf_nextActDefName = null
					let prochild = _this.approvalStatus? _this.$refs.approvalBox:_this.$refs.prochild
					if (prochild.receiveUserList.length < 1 && prochild.nextData
						.actDefType !==
						'endEvent') {
						return Promise.reject(_this.submitLabel + _this.$t(
							`doc.this_dept_select_user_alert`))
					}
					prochild.receiveUserList.forEach((element) => {
						wf_receivers.push({
							receiveUserId: element.id,
							receiveUserOrgId: element.parentId,
						});
					});
					wf_nextActDefId = prochild.nextData.actDefId;
					wf_nextActDefName = prochild.nextData.actDefName;

					if (_this.pListData && _this.pListData.procInstId) {
						//流程执行参数
						formData.bpmClientInputModel = {
							model: {
								wf_procDefKey: _this.procDefKey,
								wf_procDefId: _this.pListData.procDefId,
								wf_procTitle: _this.formData.applyTitle,
								wf_curActInstId: _this.pListData.curActInstId,
								wf_sendUserId: _this.userInfo.userName,
								wf_sendUserOrgId: _this.userInfo.deptId,
								wf_receivers: wf_receivers,
								wf_nextActDefId: wf_nextActDefId,
								wf_curComment: _this.formSubmit.summary,
								wf_curActDefId: _this.pListData.curActDefId,
								wf_curActDefName: _this.pListData.curActDefName,
								wf_nextActDefName: wf_nextActDefName,
							},
							order: _this.pListData.actDefOrder,
							applyStatus: _this.formSubmit.pass,
							review: _this.attributeModel('shenhe')||_this.attributeModel('pizhun'),
							type: formData.type
						};
					} else {
						//创建流程参数
						formData.bpmClientInputModel = {
							type: formData.type,
							model: {
								wf_procTitle: _this.formData.applyTitle,
								wf_nextActDefId: wf_nextActDefId,
								wf_procDefId: _this.pListData.procDefId,
								wf_procDefKey: _this.procDefKey,
								wf_sendUserId: _this.userInfo.userName,
								wf_sendUserOrgId: _this.userInfo.deptId,
								wf_receivers: wf_receivers,
								wf_curActDefName: _this.pListData.actDefName,
								wf_curActDefId: _this.pListData.actDefId,
								wf_nextActDefName: wf_nextActDefName,
							},
							order: _this.pListData.actDefOrder,
							review: _this.attributeModel('shenhe') || _this.attributeModel('pizhun'),
							applyStatus: _this.formSubmit.pass,
						};
					}
					if (_this.attributeModel('fabu') && wf_nextActDefId === 'end') {
						//办结
						formData.recordStatus = 'done'
					} else {
						//进行中
						formData.recordStatus = 'doing'
					}
					formData.editStatus = _this.editStatus
					formData.distributeList = this.distributeList
					let boxFormData = _this.$refs.distributeBox.formData
					let boxSetting = _this.$refs.distributeBox.setting
					for (let item in boxSetting) {
						formData[boxSetting[item]] = boxFormData[item]
					}
					return addExtraApply(formData).then((res) => {
						if (res.code === 200) {
							this.$refs.uToast.show({
								type: 'success',
								icon: false,
								message: this.$t(`doc.this_dept_process_sub_succ`),
								duration: 2000, //显示时间, 毫秒。设为 0 则不会自动关闭，建议1200
								complete: () => {
									_this.close();
								}
							})
						}
					});
				}).then(() => {
					uni.hideLoading();
				}).catch((err) => {
					uni.hideLoading();
					if (Array.isArray(err)) {
						err = 'Form item not empty'
					}
					console.log("表单校验不成功", err)
					this.$refs.uToast.show({
						type: 'error',
						duration: 5000,
						icon: false,
						message: err
					})

				})
			},
			getSettingDocClassTreeseList() {
				let query = {
					classStatus: "1"
				}
				if (this.formData.classType === this.classTypeForeign) {
					query.neClassType = undefined
					query.classType = this.classTypeForeign
				} else {
					query.neClassType = this.classTypeForeign
					query.classType = undefined
				}
				settingDocClassList(query).then(
					(response) => {
						this.docClassList = JSON.parse(JSON.stringify(response.rows))
					}
				);
			},

			formatterDocClass(cellValue) {
				let _this = this
				console.log("docClassList", _this.docClassList)
				let item = _this.docClassList.find(item => item.id === cellValue)
				return item ? item.className : cellValue
			},
			openMonitor() {
				uni.$u.route('/pages/workflowList/monitor?procInstId=' + this.procInstId)
				//window.open(process.env.VUE_APP_FLOW_IMG_PATH + processInstanceId);
			},
			close() {
				uni.switchTab({
					url: '/pages/index'
				});
			},
		},
	};
</script>
<style scoped>
	.document_change_add {
		.fujian .el-textarea__inner {
			border: 0 solid #dcdfe6;
			padding: 0;
		}
	}
</style>
