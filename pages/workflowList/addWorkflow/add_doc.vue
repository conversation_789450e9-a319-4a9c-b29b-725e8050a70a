<template>
	<view class="u-page">
		<!-- 文件新增 -->

		<page-header :procInstId="procInstId" :title="$t(`doc.this_dept_file`)+$t(`doc.this_dept_new_add`)">
			<u--form labelPosition="left" labelWidth="30%" :rules="rules" :model="formData" ref="form1">
				<view class="white-card">
					<view class="card-head">
						<!-- 基础信息 -->
						<view class="head-title">{{$t(`doc.this_dept_base_msg`)}}</view>
					</view>
					<!--card-head卡片标题-->
					<!-- 文件类型 -->
					<u-form-item :label="$t(`doc.this_dept_file_types`)" borderBottom prop="docClass">
						{{docClassData.className}}
					</u-form-item>
					<!-- 变更类型 -->
					<u-form-item :label="$t(`doc.this_dept_change_type`)" borderBottom prop="changeType">
						<span>{{$t(`doc.this_dept_new_add`)}}</span>
					</u-form-item>
					<!-- 归属上级文件 -->
					<u-form-item :label="$t(`doc.this_dept_by_superior`)" v-if="formData.classType===classTypeRecord"
						borderBottom prop="upDocName">
						{{formData.upDocName}}
					</u-form-item>
					<!-- 上级文件编号 -->
					<u-form-item :label="$t(`doc.this_dept_superior_file_name`)"
						v-if="formData.classType===classTypeRecord" borderBottom prop="parentDocId">
						{{formData.parentDocId}}
					</u-form-item>
					<!-- 文件名称 -->
					<u-form-item :label="$t(`doc.this_dept_file_names`)" borderBottom prop="docName">
						{{formData.docName}}
					</u-form-item>
					<!-- 文件编号 -->
					<u-form-item :label="$t(`doc.this_dept_file_codes`)" borderBottom prop="docId">
						{{formData.docId}}
					</u-form-item>
					<!-- 内部文件编号 -->
					<u-form-item v-if="internalDocIdShow" :label="$t(`doc.this_dept_internal_file_number`)" borderBottom
						prop="internalDocId">
						{{formData.internalDocId}}
					</u-form-item>
					<!-- ECN编号 -->
					<u-form-item v-if="ecnCodeShow" :label="$t(`doc.this_dept_ecn_number`)" borderBottom prop="ecnCode">
						{{formData.ecnCode}}
					</u-form-item>
					<!-- 文件版本 -->
					<u-form-item :label="$t(`doc.this_dept_file_versions`)" borderBottom prop="versionValue">
						{{formData.versionValue}}
					</u-form-item>
					<!-- 编制部门 -->
					<u-form-item :label="$t(`doc.this_dept_staffing_depts`)" borderBottom prop="deptName">
						{{formData.deptName}}
					</u-form-item>
					<!-- 编制人 -->
					<u-form-item :label="$t(`doc.this_dept_staff`)" borderBottom prop="nickName">
						{{formData.nickName}}
					</u-form-item>
					<!-- 编制时间 -->
					<u-form-item :label="$t(`doc.this_dept_preparation_time`)" borderBottom prop="applyTime">
						{{parseTime(formData.applyTime,'{y}-{m}-{d} {h}:{i}')}}
					</u-form-item>
					<!-- 文件模版 -->
					<u-form-item :label="$t(`doc.this_dept_file_temp`)" borderBottom>
						<u--text type="primary" 　@click="handlePreview(mobanwenjian[0].id)"
							v-if="mobanwenjian.length !== 0" :text="mobanwenjian[0].fileName">
						</u--text>
					</u-form-item>
					<!-- 文件来源 -->
					<u-form-item :label="$t(`doc.this_dept_change_source`)" borderBottom prop="invokeId">
						<u--text type="primary" @click="handleDeal()" :text="formData.invokeId"></u--text>
					</u-form-item>

					<!-- 法规/标准相关字段 -->
					<view v-if="regulationStatusShow || regulationPublishDateShow">
						<!-- 法规标准状态 -->
						<u-form-item v-if="regulationStatusShow" :label="$t('dict.regulation_standard_status')" borderBottom prop="regulationStandardStatus">
							<u-select
								v-if="editStatus"
								v-model="formData.regulationStandardStatus"
								:list="dict.type.regulation_standard_status"
								:placeholder="$t('doc.places_select') + $t('dict.regulation_standard_status')"
								value-name="value"
								label-name="label"
								@change="regulationStatusChange"
							></u-select>
							<dict-tag v-else :options="dict.type.regulation_standard_status" :value="formData.regulationStandardStatus" />
						</u-form-item>

						<!-- 法规发布日期 -->
						<u-form-item v-if="regulationPublishDateShow" :label="$t('field.regulation_publish_date')" borderBottom prop="regulationPublishDate">
							<u-datetime-picker
								v-if="editStatus"
								v-model="formData.regulationPublishDate"
								mode="date"
								:placeholder="$t('doc.places_select') + $t('field.regulation_publish_date')"
								format="YYYY-MM-DD"
								@change="regulationPublishDateChange"
							></u-datetime-picker>
							<u--text v-else :text="formData.regulationPublishDate || '--'"></u--text>
						</u-form-item>
					</view>

					<!-- 法规实施日期 -->
					<u-form-item v-if="regulationImplementDateShow" :label="$t('field.regulation_implement_date')" borderBottom prop="regulationImplementDate">
						<u-datetime-picker
							v-if="editStatus"
							v-model="formData.regulationImplementDate"
							mode="date"
							:placeholder="$t('doc.places_select') + $t('field.regulation_implement_date')"
							format="YYYY-MM-DD"
							@change="regulationImplementDateChange"
						></u-datetime-picker>
						<u--text v-else :text="formData.regulationImplementDate || '--'"></u--text>
					</u-form-item>




          <template v-if="!(pButton == 'DISUSE')">
						<!-- 当前变更版本 -->
						<u-form-item :label="$t(`doc.this_dept_current_change_ver`)" borderBottom
							prop="standardDocfileList" class="item-right-col">
							<u--text type="primary" @click="handlePreview(item.url)"
								v-for="(item, index) in standardDocfileList" :key='index' :text="item.name">
							</u--text>
						</u-form-item>
						<!-- 变更版本附件 -->
						<u-form-item :label="$t(`doc.this_dept_change_ver_annex`)" borderBottom class="item-right-col">
							<u--text type="primary" @click="handlePreview(item.url)"
								v-for="(item, index) in appendixesfileList" :key='index' :text="item.name">
							</u--text>
						</u-form-item>
					</template>
					<!-- 项目编码 -->
					<template v-if="projectCodeShow">
						<u-form-item :label="$t(`doc.this_dept_project_code`)" borderBottom prop="projectCode">
              <dict-tag :options="dict.type.project_code_list" :value="item" v-for="item in formData.projectCode"
                        :key="item"/>
						</u-form-item>
					</template>

					<!-- 项目人员配置 -->
					<view v-if="projectPersonList.length > 0">
						<u-form-item :label="$t('project_person.users')" borderBottom>
							<!-- 人员配置标题 -->
						</u-form-item>
						<view v-for="(person, index) in projectPersonList" :key="index">
							<u-form-item :label="$t('login.username')" >
								{{ person.userAccount }}
							</u-form-item>
							<u-form-item :label="$t('doc.this_dept_name')" >
								{{ person.userName }}
							</u-form-item>
							<u-form-item :label="$t('doc.this_dept_dept')" borderBottom>
								{{ person.userDept }}
							</u-form-item>
						</view>
					</view>

					<template v-if="projectNameSecurityKeywordByteShow">
						<!-- 项目编码 -->
						<u-form-item v-if="nodeShow('doc_recheck_act')||formData.projectName"
							:label="$t(`doc.this_dept_project_name`)" borderBottom prop="projectName">
							{{formData.projectName}}
						</u-form-item>
						<u-form-item v-if="nodeShow('doc_recheck_act')||formData.securityClass"
							:label="$t(`doc.this_dept_security_class`)" borderBottom prop="securityClass">
							<dict-tag :options="dict.type.security_class_list" :value="formData.securityClass" />
						</u-form-item>
						<u-form-item v-if="nodeShow('doc_recheck_act')||formData.keyword"
							:label="$t(`doc.this_dept_keyword`)" borderBottom prop="keyword">
							{{formData.keyword}}
						</u-form-item>
						<u-form-item v-if="nodeShow('doc_recheck_act')||formData.docBytes"
							:label="$t(`doc.this_dept_doc_bytes`)" borderBottom prop="docBytes">
							{{formData.docBytes}}
						</u-form-item>
					</template>
					<!-- 体系条款 -->
					<template v-if="systemClauseShow">
						<u-form-item :label="$t(`doc.system_clause`)" borderBottom prop="systemClause">
							<dict-tag :options="dict.type.system_clause_list" :value="formData.systemClause" />
						</u-form-item>
					</template>
					<!-- 客户编码 -->
					<template v-if="formCustomerShow">
						<u-form-item :label="$t(`doc.this_dept_client_codes`)" borderBottom prop="customerCode">
							{{formData.customerCode}}
						</u-form-item>
					</template>
					<!-- 产品表格 -->
					<template v-if="isShowPart">
						<u-form-item :label="$t(`doc.this_dept_product_codes`)" borderBottom>
							<!-- 编辑状态下的产品表格 -->
							<template v-if="editStatus">
								<view class="product-table-container">
									<view class="table-header">
										<u-button type="primary" size="mini" @click="addProductTableRow"
											:text="$t('doc.this_dept_add_row')" style="margin-bottom: 10px;"></u-button>
									</view>

									<view v-for="(item, index) in productTableData" :key="index" class="product-row">
										<view class="row-header">
											<text class="row-title">{{ $t('doc.this_dept_product_item') }} {{ index + 1 }}</text>
											<u-button v-if="productTableData.length > 1" type="error" size="mini"
												@click="removeProductTableRow(index)" :text="$t('doc.this_dept_delete')"></u-button>
										</view>

										<!-- 产品代码 -->
										<u-form-item :label="$t(`doc.this_dept_product_codes`)" borderBottom
											:prop="'productTableData.' + index + '.materialCode'" :rules="materialCodeRules">
											<u--input v-model="item.materialCode"
												:placeholder="$t(`doc.this_dept_insert`)"
												maxlength="20"
												clearable
												show-word-limit></u--input>
										</u-form-item>

										<!-- 产品版本 -->
										<u-form-item v-if="isShowProductVersion" :label="$t(`doc.this_dept_product_version`)" borderBottom
											:prop="'productTableData.' + index + '.productVersion'" :rules="productVersionRules">
											<u--input v-model="item.productVersion"
												:placeholder="$t(`doc.this_dept_insert`)"
												maxlength="20"
												clearable
												show-word-limit></u--input>
										</u-form-item>

										<!-- 产品描述 -->
										<u-form-item :label="$t(`doc.this_dept_product_summarys`)" borderBottom
											:prop="'productTableData.' + index + '.materialDescription'" :rules="materialDescriptionRules">
											<u--textarea v-model="item.materialDescription"
												:placeholder="$t(`doc.this_dept_insert`)"
												maxlength="200"
												show-word-limit></u--textarea>
										</u-form-item>
									</view>
								</view>
							</template>

							<!-- 只读状态下的产品表格 -->
							<template v-else>
								<uni-table border emptyText="">
									<uni-tr>
										<uni-th>{{ $t(`doc.this_dept_product_codes`) }}</uni-th>
										<uni-th v-if="isShowProductVersion">{{ $t(`doc.this_dept_product_version`) }}</uni-th>
										<uni-th>{{ $t(`doc.this_dept_product_summarys`) }}</uni-th>
									</uni-tr>
									<uni-tr v-for="(item, index) in productTableData" :key="index">
										<uni-td style="word-break: break-all;">{{ item.materialCode || '-' }}</uni-td>
										<uni-td v-if="isShowProductVersion" style="word-break: break-all;">{{ item.productVersion || '-' }}</uni-td>
										<uni-td style="word-break: break-all;">{{ item.materialDescription || '-' }}</uni-td>
									</uni-tr>
								</uni-table>
							</template>
						</u-form-item>

						<!-- 工厂 -->
						<u-form-item :label="$t(`doc.this_dept_factorys`)" borderBottom prop="factorys">
							<dict-tag :options="dict.type.tenant_list" :value="formData.factorys" />
						</u-form-item>
					</template>

          <template v-if="filePurposeShow">
            <!--            文件用途-->
            <u-form-item :label="$t(`dict.file_purpose_type`)" borderBottom prop="filePurpose">
              <dict-tag :options="dict.type.file_purpose_type" :value="formData.filePurpose"/>
            </u-form-item>
          </template>

          <template v-if="formDeviceShow">
						<!-- 设备编码 -->
						<u-form-item :label="$t(`doc.this_dept_unit_codes`)" borderBottom prop="deviceCode">
							{{formData.deviceCode}}
						</u-form-item>
						<!-- 设备名称 -->
						<u-form-item :label="$t(`doc.this_dept_unit_name`)" borderBottom prop="deviceName">
							{{formData.deviceName}}
						</u-form-item>
					</template>
					<!-- 产品版本 -->
					<template v-if="formProductVersionShow">
						<u-form-item :label="$t(`doc.this_dept_product_version`)" borderBottom prop="productVersion">
							{{formData.productVersion}}
						</u-form-item>
					</template>
					<!-- 保存期限 -->
					<template v-if="shelfLifeShow">
						<u-form-item :label="$t(`doc.this_dept_shelf_life`)" borderBottom prop="shelfLife">
							{{parseTime(formData.shelfLife,'{y}-{m}-{d}')}}
						</u-form-item>
					</template>
					<!-- 合规性 -->
					<u-form-item v-if="formData.classType===classTypeForeign&&complianceShow" :label="$t(`doc.this_dept_compliancy`)"
						borderBottom prop="compliance">
						<u--textarea v-model="formData.compliance" :disabled="true"></u--textarea>
					</u-form-item>
					<!-- 变更前内容 -->
					<u-form-item :label="$t(`doc.this_dept_change_reason`)" borderBottom prop="changeReason">
						<u--textarea v-model="formData.changeReason" :disabled="true"></u--textarea>
					</u-form-item>
					<!-- 变更后内容 -->
					<u-form-item :label="$t(`doc.this_dept_changes`)" borderBottom prop="content">
						<u--textarea v-model="formData.content" :disabled="true"></u--textarea>
					</u-form-item>
					<!-- 备注 -->
					<u-form-item :label="$t(`sys_mgr.user_remark`)+`:`" prop="remark">
						<u--textarea v-model="formData.remark" :disabled="true"></u--textarea>
					</u-form-item>
					<!-- 后期处理 培训记录 -->
					<u-form-item
						v-if="(trains&&trains.length>0)||(workflowStatus&&nodeShow('page_oper_add_train_record'))"
						:label="$t(`doc.this_dept_train_record`)" borderBottom>
						<!-- 后期修改，PC端为上传文件 -->
						<u--text type="primary" v-for="(item, index) in trains" @click="handlePreview(item.url)"
							:key='index' :text="item.name"></u--text>
					</u-form-item>
					<!-- 是否上传客户记录 -->
					<u-form-item v-if="nodeShow('whether_customer_record')||formData.whetherCustomer"
						:label="$t(`doc.whether_customer_records`)" borderBottom labelWidth="140"
						prop="whetherCustomer">
						<dict-tag :options="dict.type.sys_yes_no" :value="formData.whetherCustomer" />
					</u-form-item>
					<!-- 客户记录 -->
					<u-form-item :label="$t(`doc.customer_record`)" borderBottom
						v-if="(customerRecordList&&customerRecordList.length>0)||(workflowStatus&&nodeShow('add_customer_record')&&formData.whetherCustomer!=='N')">
						<!-- 后期修改，PC端为上传文件 -->
						<u--text mode=" link" type="primary" v-for="(item, index) in customerRecordList"
							@click="handlePreview(item.url)" :key='index' :text="item.name"></u--text>
					</u-form-item>
				</view>
				<view class="white-card" v-if="!!formData.yNTrain">
					<view class="card-head">
						<!-- 培训 -->
						<view class="head-title">{{ $t(`doc.this_dept_train`) }}</view>
					</view>
					<!-- 是否培训 -->
					<u-form-item :label="$t(`doc.this_dept_train_or_not`)" borderBottom prop="yNTrain">
						<dict-tag :options="dict.type.sys_yes_no" :value="formData.yNTrain" />
					</u-form-item>
				</view>
			</u--form>
			<distribute-box v-if="!batchStatus" :editStatus="editStatus" :workflowStatus="workflowStatus"
				:setDeptReceiver="nodeShow('set_dept_receiver')" :distributeList="distributeList" ref="distributeBox"></distribute-box>

			<u-collapse :value="['1']" v-if="formData.classType===classTypeDoc">
				<view class="white-card">
					<u-tabs class="tabs-2" :list="list1" @click="state" lineColor="#333"
						:activeStyle="{color: '#333', transform: 'scale(1)'}"
						:inactiveStyle="{ color: '#666', transform: 'scale(1)'}">
					</u-tabs>

					<linkNote :editStatus="editStatus" v-if="tabIndex == 0 && classTypeRecordMN"
						:dataList="formData.noteLinks" ref="linkNote" :dataType="formData.dataType"></linkNote>
					<link-record v-if="tabIndex == 0 &&　!classTypeRecordMN" :dataList="formData.recordLinks"
						:status="false">
					</link-record>
					<link-file v-if="tabIndex == 1" :dataList="formData.docLinks">
					</link-file>
					<!-- 				<historical-version
					v-if="tabIndex == 2"
					:dataList="formData.versions"
				>
				</historical-version> -->
				</view>
			</u-collapse>
			<!-- <workflow-logs :procInstId="procInstId"></workflow-logs> -->
			<approval-box v-if="!nodeShow('top_btn_publish_file')&&workflowStatus&&approvalStatus&&!batchStatus"
				ref="approvalBox" :submitLabel="submitLabel" :selected="nodeShow('default_selected')"
				:order="parseInt(order)" :searchQuery="searchQuery" :hideNodeCode="hideNodeCode"
				:defaultStaff="defaultStaff" :pListData="pListData"
				:status="(nodeShow('shenhe')||nodeShow('pizhun'))"></approval-box>
			<view class="white-card"
				v-if="(nodeShow('shenhe')||nodeShow('pizhun'))&&workflowStatus&&!approvalStatus&&!batchStatus">
				<view class="card-head">
					<view class="head-title">{{submitLabel}}</view>
				</view>
				<u--form labelPosition="left" :model="formSubmit" :rules="rules" ref="validateForm">
					<!-- 结论 -->
					<u-form-item required :label="submitLabel+$t(`doc.this_dept_conclusion`) " prop="pass" borderBottom
						labelWidth="30%">
						<u-radio-group v-model="formSubmit.pass" @change="commentItemSelect" placement="row">
							<u-radio style="margin-right: 10px;" v-for="(item,index) in passoptions" :key="index"
								:name="item.value" :label="item.label"></u-radio>
						</u-radio-group>
					</u-form-item>
					<!-- 意见 -->
					<u-form-item required :label="submitLabel + $t(`doc.this_dept_comments`) " borderBottom
						labelWidth="30%" prop="summary" class="label-top">
						<u--textarea v-model="formSubmit.summary"
							:placeholder="$t(`doc.this_dept_insert`)+submitLabel+$t(`doc.this_dept_comments`)"></u--textarea>
					</u-form-item>
				</u--form>
			</view>
			<processcode v-if="!nodeShow('top_btn_publish_file')&&workflowStatus&&!approvalStatus&&!batchStatus"
				ref="prochild" :order="order" :searchQuery="searchQuery" :hideNodeCode="hideNodeCode"
				:defaultStaff="defaultStaff" :selected="nodeShow('default_selected')"></processcode>
			<view class="foot-btn">
				<u-row>
					<u-col span="6">
						<u-button type="primary"
							v-if="!nodeShow('top_btn_publish_file')&&!editStatus&&workflowStatus&&!batchStatus"
							:text="$t('doc.this_dept_annex')" @click="submitForm"></u-button>
					</u-col>
					<u-col span="6">
						<u-button type="success" :text="$t('doc.this_dept_process_monitor')"
							@click="openMonitor"></u-button>
					</u-col>
				</u-row>
			</view>
			<u-toast ref="uToast"></u-toast>
		</page-header>
	</view>
</template>

<script>
import {
  getExtAttributeModel,
  getRecordbyPorcInstId,
  getRedirectDefId,
  procInstInfoAndStatus,
} from '@/api/my_business/workflow'
import {
  addModifyApply,
  getInfoByBpmnId,
  getModifyApply,
  queryModifyApplyTrain,
  validateByUserName
} from "@/api/file_processing/modifiyApply";
import {settingDocClassId} from "@/api/file_settings/type_settings";
import {modifyApplyLinklist} from "@/api/document_account/standard";
import {getInfo, getInfoBy} from "@/api/setting/docClassFlowNodeDetail";
import {listPresetUser} from '@/api/setting/presetUser'
import {getProjectPersonByCode} from '@/api/setting/projectPerson'
import linkRecord from "@/pages/common/linkRecord.vue";
import linkFile from "@/pages/common/linkFile.vue";
import linkNote from "@/pages/common/linkNote.vue";

import historicalVersion from "@/pages/common/historicalVersion.vue";
import workflowLogs from "@/pages/common/workflowLogs/index.vue";
import processcode from "@/pages/common/processcode/index.vue";
import {listModifyApplyDistribute} from '@/api/document_account/extraApply';
import {listDept} from '@/api/system/dept';
import {getCompanyList, getLeaderByUserName} from '@/api/system/user';
import DistributeBox from './add_import/distributeBox.vue'
import {listWorkflowLog} from '@/api/my_business/workflowLog';
import PageHeader from "./add_import/pageHeader.vue";
import approvalBox from './add_import/approvalBox.vue';
import {listDistributeGroupDetail} from '@/api/setting/distributeGroupDetail'

export default {
  dicts: ["business_status", "tenant_list", 'sys_yes_no', 'system_clause_list', 'project_code_list', 'file_purpose_type',
			'security_class_list'
		],
		components: {
			linkRecord,
			linkFile,
			historicalVersion,
			workflowLogs,
			processcode,
			DistributeBox,
			linkNote,
			PageHeader,
			approvalBox
		},
		data() {
			let validateFileUrl = (rule, value, callback) => {
				if (this.standardDocfileList.length < 1) {
					//我控制了FileList 长度代表文件个数
					callback(new Error("请上传文件"));
				} else {
					callback();
				}
			};
			let validateDocName = (rule, val, callback) => {
				if (this.editStatus) {
					if (this.formData.docName != "" && this.formData.docName != undefined) {
						isExistByName({
							docName: this.formData.docName,
							applyId: this.formData.id
						}).then((response) => {
							if (response.data == true) {
								return callback(new Error("文件名已经存在，请重新输入"));
							} else {
								callback();
							}
						});
					} else {
						callback();
					}
				} else {
					callback();
				}
			};
			return {
				mark: undefined,
				partNumberArr: [""],
				// 产品表格数据
				productTableData: [
					{
						materialCode: '',
						productVersion: '',
						materialDescription: '',
					}
				],
				// 控制产品表格显示
				isShowPart: false,
				isShowProductVersion: false,
				docClassData: {
					className: ''
				},
				distributeSetting: {
					trainType: 'trainType',
					distributeType: 'distributeType',
					isDistribute: 'yNDistribute'
				},
				approvalStatus: true,
				order: 0,
				settingDetail: {},
				type: 'add_doc',
				list1: [{

					name: this.$t(`doc.this_dept_related_record`),
				}, {
					name: this.$t(`doc.this_dept_related_file`),
				}],
				tabIndex: 0,
				defaultStaff: undefined,
				classTypeRecord: 'RECORD',
				classTypeDoc: 'DOC',
				classTypeForeign: 'FOREIGN',
				pButton: "ADD",
				classTypeNote: 'NOTE',
				searchQuery: {},
				shlkPath: process.env.VUE_APP_SHLK_PATH,
				dealDrawerShow: false,
				submitLabel: this.$t('doc.this_dept_annex'),
				isProject: false,
				shenchenbianhao: false,
				formSubmit: {
					summary: "",
					actionType: "",
					pass: undefined
				},
				isSummary: false,
				title: '文件新增',
				projectList: [],
				project: {
					id: '',
					name: ''
				},
				classTypeRecordMN: false,
				activeName: "info",
				nodeDetail: {},
				procDefKey: undefined,
				processData: {},
				viewId: "",
				userInfo: this.$store.getters.user,
				viewShow: false,
				active: 4,
				activeIndex: "2",
				uploadType: ["doc", "docx", "ppt", "xlsx", "pdf", "jpg", "png"],
				passoptions: [{
						value: true,
						label: this.$t(`doc.this_dept_pass`)
					},
					{
						value: false,
						label: this.$t(`doc.this_dept_not_pass`)
					},
				],
				monitorDrawerVisible: false,
				redirectDefId: undefined,
				redirectReceivers: undefined,
				redirectOrder: undefined,
				showProcessCode: false,
				formData: {
					classType: undefined,
					projectId: undefined,
					projectName: undefined,
					docClass: undefined,
					changeType: undefined,
					docName: undefined,
					versionValue: "",
					docId: undefined,
					deptId: undefined,
					deptName: undefined,
					userName: undefined,
					nickName: undefined,
					currentVersion: undefined,
					changeReason: undefined,
					content: undefined,
					trainDept: undefined,
					dataType: undefined,
					applyTime: undefined,
					appendixes: undefined, //附件
					standardDoc: {
						fileName: ''
					}, //编制文件
					docLinks: undefined, //关联文件
					recordLinks: undefined, // 关联记录
					preStandardDoc: "",
					preAppendixes: "",
					preChangeCode: undefined,
					step: undefined,
					presetUserList: [],
					batch: undefined,
					// 法规/标准相关字段
					regulationStandardStatus: undefined, // 法规标准状态
					regulationPublishDate: undefined, // 法规发布日期
					regulationImplementDate: undefined, // 法规实施日期
				},
				// 产品表格验证规则
				materialCodeRules: [
					{required: true, message: this.$t(`doc.this_dept_product_code_no_null`), trigger: ['blur', 'change']}
				],
				productVersionRules: [
					{required: true, message: this.$t(`doc.this_dept_product_version_no_null`), trigger: ['blur', 'change']}
				],
				materialDescriptionRules: [
					{required: true, message: this.$t(`doc.this_dept_product_summary_no_null`), trigger: ['blur', 'change']}
				],
				// 产品表格整体验证规则
				productTableRules: {
					productTableData: [
						{
							validator: (rule, value, callback) => {
								if (this.isShowPart && (!value || value.length === 0)) {
									callback(new Error(this.$t(`doc.this_dept_product_table_no_null`)));
								} else {
									callback();
								}
							},
							trigger: ['blur', 'change']
						}
					]
				},
				rules: {
					whetherCustomer: [{
						required: true,
						message: this.$t(`doc.this_dept_pls_select`) + '!',
						trigger: "blur,change"
					}, ],
					fileEffectiveDate: [{
						required: true,
						message: this.$t(`doc.this_dept_select_effective_date`),
						trigger: "blur,change"
					}, ],
					yNTrain: [{
						required: true,
						message: this.$t(`doc.this_dept_select_is_train`),
						trigger: "blur,change"
					}, ],
					yNDistribute: [{
						required: true,
						message: this.$t(`doc.this_dept_select_is_distribute`),
						trigger: "blur,change"
					}, ],
					// 法规相关字段验证规则
					regulationStandardStatus: [{
						required: false,
						message: this.$t('doc.places_select') + this.$t('dict.regulation_standard_status'),
						trigger: "blur,change"
					}],
					regulationPublishDate: [{
						required: false,
						message: this.$t('doc.places_select') + this.$t('field.regulation_publish_date'),
						trigger: "blur,change"
					}],
					regulationImplementDate: [{
						required: false,
						message: this.$t('doc.places_select') + this.$t('field.regulation_implement_date'),
						trigger: "blur,change"
					}],
					projectId: [{
						required: true,
						message: this.$t(`doc.this_dept_select_project`),
						trigger: "blur,change"
					}, ],
					pass: [{
						required: true,
						message: this.$t(`doc.this_dept_pls_select`),
						trigger: "blur,change"
					}, ],
					docClass: [{
						required: true,
						message: this.$t(`doc.this_dept_preparer`),
						trigger: "change",
					}],
					upVersionId: [{
						required: true,
						message: this.$t(`doc.this_dept_select_up_file`),
						trigger: "blur,change",
					}],
					docName: [{
							required: true,
							message: this.$t(`doc.this_dept_file_name_not_null`),
							trigger: "blur,change",
						}, {
							max: 1000,
							message: this.$t(`doc.this_dept_file_name_more_long`),
						},
						{
							validator: validateDocName,
							trigger: "blur"
						},
					],
					versionValue: [{
						required: true,
						message: this.$t(`doc.this_dept_insert_ver`),
						trigger: "blur,change"
					}, ],
					custodyDeptId: [{
						required: true,
						message: this.$t(`file_handle.change_select_dept`),
						trigger: "blur,change"
					}, ],
					shelfLife: [{
						required: true,
						message: this.$t(`file_handle.change_select_term`),
						trigger: "blur,change"
					}, ],
					compliance: [{
							required: true,
							message: this.$t(`doc.this_dept_ins_compliancy`),
							trigger: "blur,change"
						},
						{
							max: 1000,
							message: this.$t(`doc.this_dept_compliancy_more_long`),
						},
					],
					changeReason: [{
							required: true,
							message: this.$t(`doc.this_dept_insert_change_reason`),
							trigger: "blur,change"
						},
						{
							max: 1000,
							message: this.$t(`doc.this_dept_change_reason_more_long`),
						},
					],
					content: [{
							required: true,
							message: this.$t(`doc.this_dept_insert_change_content`),
							trigger: "blur,change"
						},
						{
							max: 1000,
							message: this.$t(`doc.this_dept_change_content_more_long`),
						},
					],
					standardDocfileList: [{
						required: true,
						validator: validateFileUrl,
						trigger: ["blur", "change", "input"],
					}, ],
					partNumber: [{
						required: true,
						message: this.$t(`doc.this_dept_product_code_no_null`),
						trigger: "blur,change",
					}],
					partRemark: [{
						required: true,
						message: this.$t(`doc.this_dept_product_summary_no_null`),
						trigger: "blur,change",
					}],

					customerCode: [{
						required: true,
						message: this.$t(`doc.this_dept_client_code_no_null`),
						trigger: "blur,change",
					}],
					deviceCode: [{
						required: true,
						message: this.$t(`doc.this_dept_unit_code_no_null`),
						trigger: "blur,change",
					}],
					deviceName: [{
						required: true,
						message: this.$t(`doc.this_dept_device_name_no_null`),
						trigger: "blur,change",
					}],
					productVersion: [{
						required: true,
						message: this.$t(`doc.this_dept_product_version_no_null`),
						trigger: "blur,change",
					}],
				},
				nodeDetailList: [],
				kuozhanshujuBool: {},
				kuozhanshuju: {},
				field117Action: "",
				action: "/dms-admin/process/file/local_upload",
				appendixesfileList: [],
				standardDocfileList: [],
				summary: "",
				pListData: {},
				editStatus: false,
				workflowStatus: false,
				dialogVisible: false,
				processcodeData: {},
				processInstanceModel: {},
				disabled: false,
				mobanwenjian: [],
				deptOptions: [],
				deptList: [],
				companyList: [],
				distributeList: [],
				loading: false,
				detailLoading: false,
				flowStepLoading: false,
				procInstId: undefined,
				shelfLifeShow: false,
				formShow: false,
				formCustomerShow: false,
				codeAndTypeShow: false,
				formDeviceShow: false,
				systemClauseShow: false,
				formProductVersionShow: false,
				projectCodeShow: false,
				internalDocIdShow: false,
				ecnCodeShow: false,
				projectNameSecurityKeywordByteShow: false,
				complianceShow: false,
        filePurposeShow: false,
        regulationStatusShow: false,
        regulationPublishDateShow: false,
        regulationImplementDateShow: false,
				customerRecordList: [],
				trains: [], // 培训记录文件
				hideNodeCode: [],
				projectPersonList: [], // 项目人员配置列表
			}
		},
		watch: {
			"formData.docClass"(val, oldVal) {
				let _this = this
				if (val !== oldVal && oldVal && _this.formData.docId) {
					_this.formData.docId = ""
				}
				if (val) {
					_this.getNodeDetailInfo()
					if (!_this.procInstId) {
						_this.getNextVersion()
						_this.getByUpDocClassAndBizType(val)
					}
				}
				settingDocClassId(val).then((response) => {
					this.formData.classType = response.data.classType
					if (response.data && response.data.fileList != null) {
						this.mobanwenjian = response.data.fileList;
					} else {
						this.mobanwenjian = []
					}
				});
			},
			"formData.projectCode"(val) {
				// 监听项目代码变化，自动获取项目人员配置
				this.onProjectCodeChange(val);
			},
		},
		computed: {
			batchStatus() {
				return !!this.formData.batchId
			}
		},
		onLoad(option) {
			let row = option
			this.procInstId = row.procInstId
			this.workflowStatus = row.status == '1'
			this.status = row.status
			this.type = row.type
			if (row.order) {
				this.order = row.order
			}
			this.mark = row.mark
			this.procInstInfoAndStatus(this.procInstId)
			if (row.batchId) {
				this.getDetailById(row.id)
			} else {
				this.getDetail(this.procInstId)
			}
			this.getConfigKey("record.doc.type").then((response) => {
				this.classTypeRecordMN = response.msg === undefined ? true : response.msg === 'true';
			})
		},
		// onShow() {
		// 	this.getDetail()
		// },
		methods: {
			// 法规标准状态变更处理
			regulationStatusChange(value) {
				this.formData.regulationStandardStatus = value;
			},
			// 法规发布日期变更处理
			regulationPublishDateChange(value) {
				this.formData.regulationPublishDate = value;
			},
			// 法规实施日期变更处理
			regulationImplementDateChange(value) {
				this.formData.regulationImplementDate = value;
			},
			// 项目代码变化处理
			onProjectCodeChange(projectCodes) {
				console.log('项目代码变化:', projectCodes);
				this.projectPersonList = [];
				if (projectCodes && projectCodes.length > 0) {
					// 将项目代码数组用逗号拼接成字符串
					let flag = Array.isArray(projectCodes);
					const projectCodeStr = flag ? projectCodes.join(',') : projectCodes;
					if (!flag) {
						this.formData.projectCode = projectCodes.split(",");
					}

					console.log('请求项目人员配置，项目代码:', projectCodeStr);
					// 直接请求接口
					getProjectPersonByCode(projectCodeStr).then(response => {
						console.log('项目人员配置响应:', response);
						if (response.data && Array.isArray(response.data)) {
							// 根据userAccount去重
							this.projectPersonList = response.data.filter((user, index, arr) =>
								arr.findIndex(u => u.userAccount === user.userAccount) === index
							);
							console.log('处理后的项目人员列表:', this.projectPersonList);
						}
					}).catch(error => {
						console.warn(`获取项目人员配置失败:`, error);
						this.projectPersonList = [];
					});
				}
			},
			commentItemSelect(val) {
				this.formSubmit.summary = this.passoptions.find(v => v.value == val).label
				this.searchQuery.pass = this.formSubmit.pass
				this.$refs.prochild.init(this.pListData)
			},
			handleDeal() {
				let _this = this
				_this.$nextTick(() => {
					let url =
						`/pages/workflowList/addWorkflow/${_this.formData.invokeType}?type=${_this.type}&preChangeCode=${_this.formData.invokeId}`
					uni.$u.route(url)
				});
			},
			getModifyApplyDistributeList(applyId) {
				let _this = this
				listModifyApplyDistribute({
					applyId: applyId
				}).then(res => {
					_this.initDistributeBox(res.data)
				})
			},
			initDistributeBox(distributeList) {
				let _this = this
				_this.distributeList = distributeList
				if (_this.$refs.distributeBox) {
					_this.$refs.distributeBox.init(_this.formData, _this.distributeSetting)
				}
			},
			getDeptList() {
				// deptLevel = 2 只显示组织层级2级以内的节点
				listDept({
					status: 0
				}).then((response) => {
					this.deptList = JSON.parse(JSON.stringify(response.data))
					this.deptOptions = this.handleTree(response.data, "deptId");
				});
			},
			getCompanyDataList() {
				let _this = this
				getCompanyList({}).then(res => {
					_this.companyList = res.data
				})
			},
			getByDocClass(docClass) {
				this.loading = true
				const queryList = ['formShow', 'formCustomerShow', 'shelfLifeShow', 'formDeviceShow',
					'formProductVersionShow', 'projectCodeShow', 'systemClauseShow', 'internalDocIdShow',
          'ecnCodeShow', 'projectNameSecurityKeywordByteShow', 'complianceShow', 'filePurposeShow', 'regulationStatusShow', 'regulationPublishDateShow', 'regulationImplementDateShow'
				]

				const queryResult = queryList.map(type => {
					return new Promise((resolve, reject) => {
						getInfoBy({
							type,
							docClass
						}).then(res => {
							this.$set(this, type, false)
							if (res.data && res.data.openFlag == 'Y') {
								this.$set(this, type, res.data.openFlag == 'Y')
							}
							return resolve()
						}).catch(() => {
							return resolve()
						})
					})

				})
				Promise.all(queryResult).finally(() => {
					this.loading = false
				})
			},
			procInstInfoAndStatus(procInstId) {
				let _this = this
				procInstInfoAndStatus(procInstId).then((res) => {
					if (res) {
						_this.procDefKey = res.procDefKey
						_this.pListData = res
					} else {
						_this.pListData = {
							procInstId: procInstId
						}
					}
					_this.getExtAttributeModel()
				});
			},
			getExtAttributeModel() {
				let _this = this
				let procDefId = _this.pListData.procDefId
				let curActDefId = _this.pListData.curActDefId || _this.pListData.actDefId
				if (procDefId && curActDefId) {
					_this.getNodeDetailInfo()
					getExtAttributeModel(
						procDefId,
						curActDefId
					).then((res) => {
						console.log("扩展属性====>", res);
						let kuozhanshujuBool = {}
						let kuozhanshuju = {}
						res.data.forEach(item => {
							if (item.objType === 'Boolean') {
								kuozhanshujuBool[item.objKey] = item.objValue
							} else {
								kuozhanshuju[item.objKey] = item.objValue
							}
						})
						_this.kuozhanshujuBool = kuozhanshujuBool;
						_this.kuozhanshuju = kuozhanshuju;
					}).finally(() => {
						_this.loading = false
					});
				} else {
					_this.kuozhanshujuBool = {}
					_this.kuozhanshuju = {}
					_this.loading = false
				}
			},
			attributeModelBool(val) {
				if (this.kuozhanshujuBool && this.kuozhanshujuBool !== {}) {
					let obj = this.kuozhanshujuBool[val]
					return !!obj && obj === 'true'
				} else {
					return false
				}
			},
			attributeModel(val) {
				return this.kuozhanshuju[val]
			},
			getNodeDetailInfo() {
				let _this = this
				let curActDefId = _this.pListData.curActDefId || _this.pListData.actDefId
				if (_this.pListData && curActDefId && _this.formData.docClass) {
					getInfo(_this.formData.docClass, _this.pButton, curActDefId).then(res => {
						let nodeDetail = {}
						res.data.forEach(item => {
							nodeDetail[item.code] = true
						})
						_this.nodeDetail = nodeDetail
						_this.nodeDetailList = res.data
						_this.editStatus = _this.nodeShow('bianji') && _this.workflowStatus
						_this.submitLabel = _this.nodeShow('pizhun') ? _this.$t(`file_handle.change_approve`) :
							_this.nodeShow('shenhe') ? _this.$t(`file_handle.change_auditing`) : _this.$t(
								'doc.this_dept_annex')
						if (!_this.batchStatus) {
							_this.jointReviewRedirect();
							_this.getWorkflowParams();
						}
					})
				}
			},
			nodeShow(code) {
				let _this = this
				if (_this.nodeDetail) {
					return !!_this.nodeDetail[code]
				} else {
					return false
				}
			},
			state(tab) {
				this.tabIndex = tab.index
			},
			getDetailById(id) {
				let _this = this
				_this.detailLoading = true
				getModifyApply(id).then(async (res) => {
					let formData = res.data;
					let res1 = await modifyApplyLinklist({
						applyId: formData.id,
						linkType: "REF_DOC"
					})
					formData.docLinks = res1.rows;
					let res2 = await modifyApplyLinklist({
						applyId: formData.id,
						linkType: "RECORD"
					})
					formData.recordLinks = res2.rows;
					let res4 = await modifyApplyLinklist({
						applyId: formData.id,
						linkType: "NOTE"
					})
					formData.noteLinks = res4.rows;
					_this.setFileList(formData)
					_this.project = {
						id: formData.projectId,
						name: formData.projectName
					}
					formData.changeType = _this.pButton;
					formData.type = _this.type
					_this.docClass = formData.docClass
					_this.formData = formData
					_this.getModifyApplyTrain(formData.id)
					_this.getModifyApplyDistributeList(formData.id)
					_this.settingDocClassId(formData.docClass);
					_this.getByDocClass(formData.docClass);
					_this.formatPartNumber(_this.formData.partNumber)
				}).finally(() => {
					_this.detailLoading = false
				});
			},
			getDetail(procInstId) {
				let _this = this
				_this.detailLoading = true
				getInfoByBpmnId(procInstId).then(async (res) => {
					let formData = res.data;
					let res1 = await modifyApplyLinklist({
						applyId: formData.id,
						linkType: "REF_DOC"
					})
					formData.docLinks = res1.rows;
					let res2 = await modifyApplyLinklist({
						applyId: formData.id,
						linkType: "RECORD"
					})
					formData.recordLinks = res2.rows;
					let res3 = await listPresetUser({
						bizId: formData.id
					})
					formData.presetUserList = res3.data
					let res4 = await modifyApplyLinklist({
						applyId: formData.id,
						linkType: "NOTE"
					})
					formData.noteLinks = res4.rows;
					_this.setFileList(formData)
					_this.project = {
						id: formData.projectId,
						name: formData.projectName
					}
					formData.changeType = _this.pButton;
					formData.type = _this.type
					_this.docClass = formData.docClass
          if (formData.projectCode) {
            formData.projectCode = formData.projectCode.split(',')
          }
					_this.formData = formData
					_this.getModifyApplyTrain(formData.id)
					_this.getModifyApplyDistributeList(formData.id)
					_this.settingDocClassId(formData.docClass);
					//查询物料是否展示
					_this.getByDocClass(formData.docClass);
					_this.formatPartNumber(_this.formData.partNumber)
				}).finally(() => {
					_this.detailLoading = false
				});
			},
			formatPartNumber(_partNumber) {
				if (_partNumber) {
					this.partNumberArr = _partNumber.split(";")
				} else {
					this.partNumberArr = [""]
				}
			},
			getModifyApplyTrain(applyId) {
				let _this = this
				let trains = []
				queryModifyApplyTrain({
					type: 'train',
					applyId: applyId
				}).then(res => {
					if (res.data) {
						res.data.forEach(item => {
							trains.push({
								url: item.fileIds,
								name: item.files[0].fileName
							})
						})
					}
					_this.trains = trains
				})
				let customerRecordList = []
				queryModifyApplyTrain({
					type: 'customer',
					applyId: applyId
				}).then(res => {
					if (res.data) {
						res.data.forEach(item => {
							customerRecordList.push({
								url: item.fileIds,
								name: item.files[0].fileName
							})
						})
					}
					_this.customerRecordList = customerRecordList
				})
			},
			setFileList(formData) {
				let _this = this
				if (formData.standardDoc) {
					if (formData.standardDoc.fileId) {
						_this.standardDocfileList = [{
							name: formData.standardDoc.docName,
							url: formData.standardDoc.fileId
						}, ];
					}
				}
				if (formData.appendixes != null) {
					formData.appendixes.forEach((element) => {
						_this.appendixesfileList.push({
							name: element.docName,
							url: element.fileId,
						});
					});
				}
			},
			async getWorkflowParams() {
				let _this = this
				//会签人员
				if (_this.nodeShow('preset_countersign')) {
					let funCondition = _this.nodeFunCondition('preset_countersign')
					if (funCondition && funCondition.nodeCode && funCondition.nodeCode.length > 0 && funCondition
						.groupId) {
						let users = [];
						let res = await listDistributeGroupDetail({
							groupId: funCondition.groupId
						})
						res.rows.forEach(item => {
							users.push({
								userName: item.receiveUserName,
								nickName: item.receiveNickName,
								deptId: item.receiveUserDeptId,
								deptName: item.receiveUserDept,
							})
						})
						funCondition.nodeCode.forEach(nodeCode => {
							let preset = _this.formData.presetUserList.find(item => item.nodeCode === nodeCode)
							if (preset) {
								preset.users = JSON.stringify(users)
							} else {
								_this.formData.presetUserList.push({
									nodeCode: nodeCode,
									users: JSON.stringify(users)
								})
							}
						})
					}
				}
				// 下个环节预选直属部门领导
				if (_this.nodeShow('next_set_leader')) {
					let funCondition = _this.nodeFunCondition('next_set_leader')
					if (funCondition && funCondition.nodeCode && funCondition.nodeCode.length > 0) {
						let users = undefined;
						if (funCondition.validate) {
							let res = await getLeaderByUserName(_this.formData.userName)
							if (res.data) {
								users = [{
									userName: res.data.userName,
									nickName: res.data.nickName,
									deptId: res.data.deptId,
									deptName: res.data.dept.deptName
								}]
							}
						} else {
							let leader = _this.$store.getters.leader
							if (leader) {
								users = [{
									userName: leader.userName,
									nickName: leader.nickName,
									deptId: leader.deptId,
									deptName: leader.dept.deptName
								}]
							}
						}
						if (users) {
							funCondition.nodeCode.forEach(nodeCode => {
								let preset = _this.formData.presetUserList.find(item => item.nodeCode ===
									nodeCode)
								if (preset) {
									preset.users = JSON.stringify(users)
								} else {
									_this.formData.presetUserList.push({
										nodeCode: nodeCode,
										users: JSON.stringify(users)
									})
								}
							})
						}
					}
				}
				//设定流程默认执行人
				let defaultStaff = []
				if (_this.formData.presetUserList.length > 0) {
					defaultStaff.push(...JSON.parse(JSON.stringify(_this.formData.presetUserList)))
				}
				if (_this.nodeShow('cdxmd') && _this.formData.batch) {
					// 文件类型设置中是否设置了 需要谁驳回就再只发送给驳回的人
					let funCondition = _this.nodeFunCondition('cdxmd')
					if (funCondition && funCondition.validate) {
						//查询本次驳回有哪些人员
						let res = await listWorkflowLog({
							batch: _this.formData.batch,
							nextDefId: _this.pListData.curActDefId,
							havaDetail: true
						})
						let nodeCode = ""
						let users = []
						res.rows.forEach(item => {
							nodeCode = item.actDefId
							users.push({
								userName: item.sender,
								nickName: item.nickName,
								deptId: item.senderDeptId,
								deptName: item.deptName
							})
						})
						if (defaultStaff.length > 0) {
							let staff = defaultStaff.find(item => item.nodeCode = nodeCode)
							if (staff) {
								staff.users = JSON.stringify(users)
							}
						} else {
							defaultStaff.push({
								nodeCode: nodeCode,
								users: JSON.stringify(users)
							})
						}
					}
				}
				console.log("defaultStaff", defaultStaff)
				_this.defaultStaff = defaultStaff
				_this.searchQuery.step = _this.formData.step ? _this.formData.step : 0
				_this.searchQuery.isTrain = _this.formData.yNTrain
				_this.searchQuery.isCustomer = _this.formData.whetherCustomer
				let hideNodeCode = []
				//下一环节未预选人员隐藏
				if (_this.nodeShow('xyhjwyxryyc')) {
					let funCondition = _this.nodeFunCondition('xyhjwyxryyc')
					if (funCondition && funCondition.nodeCode) {
						let length = funCondition.nodeCode.length
						//下一环节隐藏范围 都隐藏
						hideNodeCode = funCondition.nodeCode
						//过滤有预选人员的环节
						defaultStaff.forEach(item => {
							if (item.users) {
								let users = JSON.parse(item.users)
								if (hideNodeCode.includes(item.nodeCode) && users && users.length > 0) {
									hideNodeCode = hideNodeCode.filter(code => code !== item.nodeCode)
								}
							}
						})
						//配置了反向节点 隐藏范围环节内都没预选人员 过滤掉反向节点
						if (funCondition.neNodeCode && hideNodeCode.length === length) {
							hideNodeCode = hideNodeCode.filter(code => !funCondition.neNodeCode.includes(code))
						}
						//填写了限定值 只能显示最多限定的数量
						if (funCondition.limitValue) {
							let limitValue = Number(funCondition.limitValue)
							//总数-隐藏数=显示数 显示数>限定数量
							if (!isNaN(limitValue) && (length - hideNodeCode.length) > limitValue) {
								//倒叙再插回去
								let reverse = funCondition.nodeCode.reverse()
								for (let item of reverse) {
									if (!hideNodeCode.includes(item)) {
										hideNodeCode.push(item)
									}
									if ((length - hideNodeCode.length) <= limitValue) {
										break
									}
								}
							}
						}
						// if (funCondition.validate) {
						//验证开启 配置了反向节点 隐藏范围环节内都有预选人员 增加反向节点
						if (funCondition.neNodeCode && hideNodeCode.length !== length && hideNodeCode.length ===
							0) {
							defaultStaff.forEach(item => {
								if (funCondition.neNodeCode.includes(item.nodeCode)) {
									hideNodeCode.push(item.nodeCode)
								}
							})
						}
						// }
					}
				}
				//隐藏环节列表
				_this.hideNodeCode = hideNodeCode
				_this.$nextTick(() => {
					if (_this.approvalStatus) {
						_this.$refs.approvalBox.init()
					} else {
						_this.$refs.prochild.init(_this.pListData)
					}
				})
			},
			jointReviewRedirect() {
				let _this = this
				if (_this.nodeShow('hscdx')) {
					getRecordbyPorcInstId(_this.procInstId).then(res => {
						if (res.data.length === 1) {
							let query = {
								docClass: _this.formData.docClass,
								bizType: _this.formData.changeType,
								code: "cdxmd",
								batch: _this.formData.batch,
							}
							return getRedirectDefId(query).then(res1 => {
								if (res1.data) {
									let funCondition = _this.nodeFunCondition('hscdx')
									if (funCondition && funCondition.nodeCode && funCondition.nodeCode
										.length === 1) {
										let next = res1.data.find(item => item.nextDefId === funCondition
											.nodeCode[0])
										if (next) {
											_this.redirectDefId = next.nextDefId
											_this.redirectReceivers = JSON.parse(next.receiver)
											_this.redirectOrder = next.actDefOrder
										}
									}
								}
							})
						}
					})
				}
			},
			async settingDocClassId(val) {
				let _this = this
				await settingDocClassId(val).then((response) => {
					_this.docClassData = response.data
				});
			},
			handlePreview(fileId) {
				let url = `/pages/pdfPreview/index?fileId=${fileId}`
				uni.$u.route(url)
			},
			openMonitor() {
				uni.$u.route('/pages/workflowList/monitor?procInstId=' + this.procInstId)
				//window.open(process.env.VUE_APP_FLOW_IMG_PATH + processInstanceId);
			},
			async submitForm() {
				let _this = this
				_this.$refs.form1.validate().then((res) => {
					uni.showLoading({
						mask: true
					});
					if (_this.$refs.distributeBox) {
						return _this.$refs.distributeBox.validateForm()
					}
				}).then(async () => {
					if (!_this.procDefKey) {
						return Promise.reject(_this.$t(`doc.this_dept_no_process_setting`))
					}
					// 校验分发培训
					if (_this.$refs.distributeBox) {
						let distributeForm = _this.$refs["distributeBox"]
						// if (!!distributeForm) {
						//     let validateValid = await distributeForm.validateForm()
						// }
						//	if (!validateValid) {
						//		return;
						//	}

						//}
					}
					//审核
					if (_this.nodeShow('shenhe') || _this.nodeShow('pizhun')) {
						if (_this.approvalStatus) {
							_this.formSubmit = _this.$refs.approvalBox.formSubmit
						}
						if (_this.formSubmit.pass === undefined) {
							return Promise.reject(_this.submitLabel + _this.$t(
								`file_handle.change_result_not_null`))
						}
						if (!_this.formSubmit.pass && _this.formSubmit.summary.trim() == '') {
							return Promise.reject(_this.$t(`doc.this_dept_pls_fill`) + _this.submitLabel +
								_this.$t(`doc.this_dept_comments`))
						}
					}
					let direction = _this.pListData.actDefOrder > _this.order
					//培训记录
					if (direction && _this.nodeShow('page_oper_add_train_record') && _this.formData
						.yNTrain !== _this.no) {
						let funCondition = _this.nodeFunCondition('page_oper_add_train_record')
						if (!funCondition || (funCondition && funCondition.validate)) {
							if (!(_this.trains && _this.trains.length > 0)) {
								return Promise.reject(_this.$t(`doc.this_dept_pls_upload_train_file`))
							}
						}
					}
					//客户记录
					if (direction && _this.nodeShow('add_customer_record') && _this.formData
						.whetherCustomer !== _this
						.no) {
						let funCondition = _this.nodeFunCondition('add_customer_record')
						if (!funCondition || (funCondition && funCondition.validate)) {
							if (!(_this.customerRecordList && _this.customerRecordList.length > 0)) {
								return Promise.reject(_this.$t(`sys_mgr_log.user_signature_upload_text1`) +
									_this.$t(
										`doc.customer_record`) + '！')
							}
						}
					}
					//复审环节
					if (_this.nodeShow('doc_recheck_act')) {
						if (!_this.formData.projectName) {
							return Promise.reject(_this.$t(`doc.this_dept_insert`) + _this.$t(
								`doc.this_dept_project_name`) + '！')
						}
						if (!_this.formData.securityClass) {
							return Promise.reject(_this.$t(`doc.this_dept_pls_select`) + _this.$t(
								`doc.this_dept_security_class`) + '！')
						}
						if (!_this.formData.keyword) {
							return Promise.reject(_this.$t(`doc.this_dept_insert`) + _this.$t(
								`doc.this_dept_keyword`) + '！')
						}
						if (!_this.formData.docBytes) {
							return Promise.reject(_this.$t(`doc.this_dept_insert`) + _this.$t(
								`doc.this_dept_doc_bytes`) + '！')
						}
					}
					//签名
					if (_this.nodeShow('log_title')) {
						// 如果是编制环节校验编制人
						if (_this.editStatus) {
							let res = await validateByUserName({
								userCode: _this.formData.userName
							})
							if (!res.data) {
								return Promise.reject(_this.$t(`doc.user_organizer_validate`));

							}
						}
						let res = await validateByUserName({
							userCode: _this.userInfo.userName
						})
						if (!res.data) {
							return Promise.reject(_this.$t(`doc.user_signature_validate`))
						}
					}
					let dialogVisible = true
					_this.formData.docLinks = [];
					if (_this.$refs.linkFile && _this.$refs.linkFile.dataList) {
						_this.$refs.linkFile.dataList.forEach((element) => {
							_this.formData.docLinks.push({
								// linkId: element.linkId,
								fileId: element.fileId,
								docId: element.docId,
								versionValue: element.versionValue,
								docName: element.docName,
								docClass: element.docClass,
								status: 1,
							});
						});
					}
					if (_this.$refs.linkRecord && _this.$refs.linkRecord.dataList) {
						_this.formData.recordLinks = _this.$refs.linkRecord.dataList;
					}
					if (_this.standardDocfileList != "") {
						_this.formData.standardDoc = {
							docName: _this.standardDocfileList[0].name,
							fileId: _this.standardDocfileList[0].url
						}
					}
					_this.formData.appendixes = [];
					_this.appendixesfileList.forEach((element) => {
						_this.formData.appendixes.push({
							fileId: element.url,
							docName: element.name,
							status: 1
						});
					});
					_this.formData.remarkDoc = [];
					uni.hideLoading()
				}).then(() => {
					_this.$modal.confirm(_this.$t(`file_handle.submit_text`), _this.$t(
						`file_handle.change_tip`)).then(() => {
						_this.handleWorkflowSubmit()
					})
				}).catch((err) => {
					uni.hideLoading()
					if (Array.isArray(err)) {
						err = 'Form item not empty'
					}
					console.log("表单校验不成功", err)
					_this.$refs.uToast.show({
						type: 'error',
						duration: 5000,
						icon: false,
						message: err
					})
				})
			},

			nodeFunCondition(code) {
				let _this = this
				let nodeDetail = _this.nodeDetailList.find(item => item.code === code)
				if (nodeDetail && nodeDetail.funCondition) {
					return JSON.parse(nodeDetail.funCondition)
				} else {
					return undefined
				}
			},
			handleWorkflowSubmit(invokeFrom) {
				let _this = this
				uni.showLoading({
					mask: true
				});
				let formData = uni.$u.deepClone(_this.formData)
				// 处理数组字段转换为字符串
				formData = _this.processFormDataForSubmit(formData)
				let wf_receivers = [];
				let wf_nextActDefId = null
				let wf_nextActDefName = null
				let direction = null
				let prochild = _this.approvalStatus ? _this.$refs.approvalBox : _this.$refs.prochild
				if (typeof(invokeFrom) == 'string' && invokeFrom == 'publish') {
					// 来源于按钮【执行发布】
					wf_nextActDefId = 'end'
					wf_nextActDefName = '结束'
					direction = true
				} else {
					if (!(prochild.nextData && prochild.nextData.actDefId)) {
						uni.hideLoading()
						return Promise.reject(this.$t(`doc.this_dept_pls_select`) + this.$t(`doc.this_dept_process_step`))
					}
					if (prochild.receiveUserList.length < 1 && prochild.nextData.actDefType !== 'endEvent') {
						uni.hideLoading()
						return Promise.reject(this.$t(`doc.this_dept_select_user_alert`))
					}
					prochild.receiveUserList.forEach((element) => {
						wf_receivers.push({
							receiveUserId: element.id,
							receiveUserOrgId: element.parentId,
						});
					});
					wf_nextActDefId = prochild.nextData.actDefId;
					wf_nextActDefName = prochild.nextData.actDefName;
					direction = _this.pListData.actDefOrder > _this.order
				}

				if (_this.pListData && _this.pListData.procInstId) {
					//流程执行参数
					formData.bpmClientInputModel = {
						model: {
							wf_procDefKey: _this.procDefKey,
							wf_procDefId: _this.pListData.procDefId,
							wf_procTitle: _this.formData.docName,
							wf_curActInstId: _this.pListData.curActInstId,
							wf_sendUserId: _this.userInfo.userName,
							wf_sendUserOrgId: _this.userInfo.deptId,
							wf_receivers: wf_receivers,
							wf_nextActDefId: wf_nextActDefId,
							wf_curComment: _this.formSubmit.summary,
							wf_curActDefId: _this.pListData.curActDefId,
							wf_curActDefName: _this.pListData.curActDefName,
							wf_nextActDefName: wf_nextActDefName,
						},
						order: _this.pListData.actDefOrder,
						review: _this.nodeShow('shenhe') || _this.nodeShow('pizhun'),
						applyStatus: _this.formSubmit.pass,
						type: _this.type,
						mark: _this.mark,
						direction: direction,
						step: _this.attributeModel("step")
					};
				} else {
					//创建流程参数
					formData.bpmClientInputModel = {
						type: _this.type,
						model: {
							wf_procTitle: _this.formData.docName,
							wf_nextActDefId: wf_nextActDefId,
							wf_procDefId: _this.pListData.procDefId,
							wf_procDefKey: _this.procDefKey,
							wf_sendUserId: _this.userInfo.userName,
							wf_sendUserOrgId: _this.userInfo.deptId,
							wf_receivers: wf_receivers,
							wf_curActDefName: _this.pListData.actDefName,
							wf_curActDefId: _this.pListData.actDefId,
							wf_nextActDefName: wf_nextActDefName,
						},
						order: _this.pListData.actDefOrder,
						review: _this.nodeShow('shenhe') || _this.nodeShow('pizhun'),
						step: _this.attributeModel("step"),
						mark: _this.mark,
						direction: direction,
						applyStatus: _this.formSubmit.pass,
					};
				}

				if (_this.nodeShow('log_title')) {
					let funCondition = _this.nodeFunCondition('log_title')
					if (funCondition && funCondition.limitValue) {
						formData.bpmClientInputModel.title = funCondition.limitValue
					}
				}
				if (_this.nodeShow('top_btn_publish_file')) {
					//办结
					formData.recordStatus = 'done'
					formData.bpmClientInputModel.jointReview = false
				} else {
					//进行中
					formData.recordStatus = 'doing'
					formData.bpmClientInputModel.jointReview = prochild.nextData.multi
				}
				if (_this.nodeShow('hscdx')) {
					formData.bpmClientInputModel.batch = formData.batch
					formData.bpmClientInputModel.redirectDefId = _this.redirectDefId
					formData.bpmClientInputModel.redirectReceivers = _this.redirectReceivers
					if (_this.redirectOrder) {
						formData.bpmClientInputModel.order = _this.redirectOrder
					}
				}
				formData.editStatus = _this.editStatus
				formData.presetUserEdit = _this.nodeShow('top_btn_preset_user') || _this.nodeShow('next_set_leader')
				formData.customerEdit = _this.nodeShow('whether_customer_record')
				return addModifyApply(formData).then((res) => {
					if (res.code === 200) {
						uni.hideLoading()
						this.$refs.uToast.show({
							type: 'success',
							icon: false,
							message: this.$t(`doc.this_dept_process_sub_succ`),
							duration: 2000, //显示时间, 毫秒。设为 0 则不会自动关闭，建议1200
							complete: () => {
								_this.close();
							}
						})
					}
				});
			},
			close() {
				uni.switchTab({
					url: '/pages/index'
				});
			},

			// 处理提交数据，将数组字段转换为字符串
			processFormDataForSubmit(formData) {
				// 将 projectCode 数组转换为字符串
				if (formData.projectCode && Array.isArray(formData.projectCode)) {
					formData.projectCode = formData.projectCode.join(',')
				}
				return formData
			}
		}
	}
</script>

<style scoped>
	.white-card {
		background: #fff;
		margin: 20rpx 0;
		border-radius: 10rpx;
		padding: 20rpx;
	}

	.card-head {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 20rpx;
		padding-bottom: 20rpx;
		border-bottom: 1px solid #f0f0f0;
	}

	.head-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}


</style>
	}
</script>