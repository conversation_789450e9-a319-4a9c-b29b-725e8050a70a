<template>
	<view class="u-page">
		<!-- 文件复审 -->
		<page-header :procInstId="procInstId" :title="$t(`doc.this_dept_file_review`)">
			<view>

				<u--form labelPosition="left" labelWidth="30%" :model="formData" :rules="rules" ref="form1">
					<view class="white-card">
						<view class="card-head">
							<view class="head-title">{{ $t(`doc.this_dept_base_msg`) }}</view>
						</view><!--card-head卡片标题-->
						<view class="ft-card">
							<view class="ctr" v-for="(item,index) in formData.itemList" :key="index">
								<!-- 文件类型 -->
								<u-form-item :label="$t(`doc.this_dept_file_type`)" borderBottom prop="docClass">
									{{formatterDocClass(item.docClass)}}
								</u-form-item>
								<!-- 文件名称 -->
								<u-form-item :label="$t(`doc.this_dept_file_name`)" borderBottom prop="docName">
									{{item.docName}}
								</u-form-item>
								<!-- 文件编号 -->
								<u-form-item :label="$t(`doc.this_dept_file_code`)" borderBottom prop="docId">
									{{item.docId}}
								</u-form-item>
								<!-- 文件版本 -->
								<u-form-item :label="$t(`doc.this_dept_file_versions2`)" borderBottom
									prop="versionValue">
									{{item.versionValue}}
								</u-form-item>
								<!-- 编制部门 -->
								<u-form-item :label="$t(`doc.this_dept_staffing_dept`)" borderBottom prop="deptName">
									{{item.deptName}}
								</u-form-item>
								<!-- 编制人 -->
								<u-form-item :label="$t(`doc.this_dept_staffs`)"
									v-if="!workflowStatus||nodeShow('jielun')" borderBottom prop="nickName">
									{{item.nickName}}
								</u-form-item>
								<template v-if="!workflowStatus||nodeShow('jielun')||nodeShow('top_btn_publish_file')">
									<!-- 结论-->
									<u-form-item :label="$t(`file_handle.change_result`)" borderBottom
										prop="reviewAction">
										<my-select :disabled="!(nodeShow('jielun')&&workflowStatus)"
											:data="actionTypeoptions" v-model="item.reviewAction"></my-select>
									</u-form-item>
									<!-- 原由-->
									<u-form-item :label="$t(`doc.company_dept_reason`)" borderBottom prop="reason">
										<u--input :disabled="!(nodeShow('jielun')&&workflowStatus)"
											v-model.trim="item.reason"></u--input>
									</u-form-item>
								</template>
							</view>
						</view><!--ft-card-->
					</view><!--white-card 白色卡片-->

					<view class="white-card">
						<view class="card-head">
							<!-- 申请信息 -->
							<view class="head-title">{{ $t(`doc.this_dept_appli_info`) }}</view>
						</view>
						<!-- 标题 -->
						<u-form-item :label="$t(`doc.this_dept_title`)" prop="applyTitle" borderBottom required>
							{{formData.applyTitle}}
						</u-form-item>
						<!-- 申请部门 -->
						<u-form-item :label="$t(`doc.this_dept_appli_dept`)" prop="deptName" borderBottom>
							{{formData.deptName}}
						</u-form-item>
						<!-- 申请人 -->
						<u-form-item :label="$t(`doc.this_dept_claimant`)" prop="nickName" borderBottom>
							{{formData.nickName}}
						</u-form-item>
						<!-- 申请时间 -->
						<u-form-item :label="$t(`doc.this_dept_appli_date`)" prop="createTime" borderBottom>
							{{formData.createTime}}
						</u-form-item>
						<!-- 申请原因 -->
						<u-form-item :label="$t(`doc.this_dept_appli_reason`)" prop="reason" required>
							{{formData.reason}}
						</u-form-item>
					</view>
				</u--form>
				<approval-box v-if="workflowStatus&&approvalStatus" ref="approvalBox"
								:submitLabel="submitLabel" :selected="attributeModel('default_selected')"
								:order="parseInt(order)" :searchQuery="searchQuery"
								:hideNodeCode="hideNodeCode" :defaultStaff="defaultStaff" :pListData="pListData"
								:status="(nodeShow('shenhe')||nodeShow('pizhun'))"></approval-box>
				<view class="white-card" v-if="(nodeShow('shenhe')||nodeShow('pizhun'))&&workflowStatus&&!approvalStatus">
					<view class="card-head">
						<view class="head-title">{{submitLabel}}</view>
					</view>
					<u--form labelPosition="left" :model="formSubmit" labelWidth="30%" :rules="rules"
						ref="validateForm">
						<!-- 结论 -->
						<u-form-item required :label="submitLabel+$t(`doc.this_dept_conclusion`) " prop="pass"
							borderBottom>
							<u-radio-group v-model="formSubmit.pass" @change="commentItemSelect" placement="row">
								<u-radio style="margin-right: 10px;" v-for="(item,index) in passoptions" :key="index"
									:name="item.value" :label="item.label"></u-radio>
							</u-radio-group>
						</u-form-item>
						<!-- 意见 -->
						<u-form-item required :label="submitLabel + $t(`doc.this_dept_comments`) " borderBottom
							class="label-top">
							<u--textarea v-model="formSubmit.summary"
								:placeholder="$t(`doc.this_dept_insert`)+submitLabel+$t(`doc.this_dept_comments`)"></u--textarea>
						</u-form-item>
					</u--form>
				</view>
				<processcode v-if="workflowStatus&&!approvalStatus" ref="prochild" :searchQuery="searchQuery" :order = "order"
					:hideNodeCode="hideNodeCode" :defaultStaff="defaultStaff" :selected="attributeModel('default_selected')"></processcode>
				<view class="foot-btn">
					<u-row>
						<u-col span="6">
							<u-button type="primary" v-if="!editStatus&&workflowStatus"
								:text="$t('doc.this_dept_annex')" @click="submitForm"></u-button>
						</u-col>
						<u-col span="6">
							<u-button type="success" :text="$t('doc.this_dept_process_monitor')"
								@click="openMonitor"></u-button>
						</u-col>
					</u-row>
				</view><!--foot-btn 固定底部按钮-->
			</view>
		</page-header>
		<u-toast ref="uToast"></u-toast>
	</view>
</template>
<!--结论存在问题-->
<script>
	import {
		settingDocClassList
	} from "@/api/file_settings/type_settings";
	import {
		settingDocClassId
	} from "@/api/file_settings/type_settings";
	import {
		isExistByName
	} from "@/api/document_account/standard";
	import {
		workflowprocesskey,
		getStartActdef,
		getExtAttributeModel,
		procInstInfoAndStatus,
		getInfoByType,
		getRecordbyPorcInstId,
		getRedirectDefId
	} from '@/api/my_business/workflow'
	// PDF本地文件预览
	import {
		getWorkflowApplyLog
	} from '@/api/my_business/workflowApplyLog'
	import {
		addReviewApply,
		getReviewApplyByBpmnId,
		updateReviewApply
	} from '@/api/document_account/reviewApply'
	import {
		listPresetUser
	} from "../../../api/setting/presetUser";
	import processcode from "@/pages/common/processcode/index.vue";
	import PageHeader from "./add_import/pageHeader.vue";
	import approvalBox from './add_import/approvalBox.vue';
	export default {
		dicts: ["business_status"],
		name: "Add_doc",
		props: ["dataType", 'data'],
		components: {
			processcode,
			PageHeader,
			approvalBox
		},
		data() {
			return {
				approvalStatus: true,
				order: 0,
				redirectDefId: undefined,
				redirectReceivers: undefined,
				redirectOrder: undefined,
				searchQuery: {},
				defaultStaff: undefined,
				hideNodeCode:[],
				passoptions: [
					{ value: true, label: this.$t(`doc.this_dept_pass`) },
					{ value: false, label: this.$t(`doc.this_dept_not_pass`) },
				],
				formSubmit: { summary: "", actionType: "", pass: undefined },
				docClass: undefined,
				classTypeRecord: 'RECORD',
				classTypeDoc: 'DOC',
				classTypeForeign: 'FOREIGN',
				open: false,
				docClassList: [],
				appendixsList: [],
				remarkfileList: [],
				changeFactor: [],
				actionTypeoptions: [{
						value: "keep",
						label: this.$t(`doc.this_dept_file_status_quo`)
					},
					{
						value: "update",
						label: this.$t(`file_set.type_revise`)
					},
					{
						value: "disuse",
						label: this.$t(`file_set.type_repeal`)
					},
				],
				submitLabel: this.$t('doc.this_dept_annex'),
				isProject: false,
				docIdData: {},
				jiluliData: [],
				shenchenbianhao: false,
				pButton: 'review',
				isSummary: false,
				project: {
					id: '',
					name: ''
				},
				activeName: "info",
				nodeDetail: [],
				procDefKey: undefined,
				processData: {},
				viewId: "",
				userInfo: this.$store.getters.user,
				viewShow: false,
				active: 4,
				activeIndex: "1",
				uploadType: ["doc", "docx", "ppt", "xlsx", "pdf", "jpg", "png"],
				monitorDrawerVisible: false,
				formData: {
					id: undefined,
					applyTitle: undefined,
					deptId: undefined,
					userName: undefined,
					reason: undefined,
					status: undefined,
					createTime: undefined,
					itemList: []
				},
				kuozhanshuju: {},
				field117Action: "",
				action: "/dms-admin/process/file/local_upload",
				appendixesfileList: [],
				standardDocfileList: [],
				menuitem: "1",
				classLevelOptions: [],
				summary: "",
				pListData: {},
				editStatus: false,
				workflowStatus: false,
				dialogVisible: false,
				processcodeData: {},
				processInstanceModel: {},
				disabled: false,
				mobanwenjian: [],
				loading: true,
				detailLoading: true,
				nodeDetailLoading: true,
				flowStepLoading: false,
				rules: {
					applyTitle: [{
						required: true,
						message: this.$t(`doc.this_dept_insert_title`),
						trigger: "blur"
					}, ],
					projectId: [{
						required: true,
						message: this.$t(`doc.this_dept_select_project`),
						trigger: "blur"
					}, ],
					pass: [{
						required: true,
						message: this.$t(`doc.this_dept_pls_select`),
						trigger: "blur"
					}, ],
					reason: [{
						required: true,
						message: this.$t(`doc.this_dept_insert_appli_reason`),
						trigger: "blur"
					}, ],
				},
			};
		},
		async onLoad(option) {

			let row = option
			this.workflowStatus = row.status == '1'
			this.status = row.status
			this.type = row.type
			this.formData.type = row.type
			this.procInstId = row.procInstId
			if (row.order) {
				this.order = row.order
			}
			this.getSettingDocClassTreeseList();
			if (row.preChangeCode) {
				let res = await getWorkflowApplyLog(row.preChangeCode)
				this.procInstId = res.data.procInstId
			}
			this.procInstInfoAndStatus(this.procInstId)
			this.getDetail(this.procInstId)
		},
		methods: {
			commentItemSelect(val) {
				this.formSubmit.summary =this.passoptions.find(v=>v.value == val).label
				this.searchQuery.pass = this.formSubmit.pass
				this.$refs.prochild.init(this.pListData)
			},
			jointReviewRedirect() {
				let _this = this
				if (_this.nodeShow('hscdx')) {
					getRecordbyPorcInstId(_this.procInstId).then(res => {
						if (res.data.length === 1) {
							let query = {
								docClass: _this.docClass,
								bizType: _this.pButton,
								code: "cdxmd",
								batch: _this.formData.batch,
							}
							getRedirectDefId(query).then(res1 => {
								if (res1.data) {
									let funCondition = _this.nodeFunCondition('hscdx')
									if (funCondition && funCondition.nodeCode && funCondition.nodeCode
										.length === 1) {
										let next = res1.data.find(item => item.nextDefId === funCondition
											.nodeCode[0])
										if (next) {
											_this.redirectDefId = next.nextDefId
											_this.redirectReceivers = JSON.parse(next.receiver)
											_this.redirectOrder = next.actDefOrder
										}
									}
								}
							})
						}
					})
				}
			},
			procInstInfoAndStatus(procInstId) {
				let _this = this
				uni.showLoading({
					mask: true
				})
				procInstInfoAndStatus(procInstId).then((res) => {
					if (res) {
						_this.procDefKey = res.procDefKey
						_this.pListData = res
					} else {
						_this.pListData = {
							procInstId: procInstId
						}
					}
					_this.getExtAttributeModel()
					if (this.$refs.prochild) {
						this.$refs.prochild.init(_this.pListData)
					}
					uni.hideLoading()
				});
			},
			formatterPassOptions(value) {
				let _this = this
				if (value) {
					let option = _this.actionTypeoptions.find(item => item.value === value)
					return option ? option.label : value
				}
				return ""
			},
			getDetail(procInstId) {
				let _this = this
				_this.detailLoading = true
				getReviewApplyByBpmnId(procInstId).then(async (res) => {
					let formData = res.data;
					let res3 = await listPresetUser({
						bizId: formData.id
					})
					formData.presetUserList = res3.data
					formData.type = _this.formData.type
					formData.applyType = _this.pButton
					_this.docClass = formData.docClass
					_this.formData = formData
					_this.getSettingDocClassTreeseList();
				}).finally(() => {
					_this.detailLoading = false
					_this.initStatus()
				});
			},
			nodeShow(code) {
				let _this = this
				if (_this.nodeDetail) {
					return !!_this.nodeDetail[code]
				} else {
					return false
				}
			},
			nodeFunCondition(code) {
				let _this = this
				let nodeDetail = _this.nodeDetailList.find(item => item.code === code)
				if (nodeDetail && nodeDetail.funCondition) {
					return JSON.parse(nodeDetail.funCondition)
				} else {
					return undefined
				}
			},
			getNodeDetailInfo() {
				let _this = this
				let curActDefId = _this.pListData.curActDefId || _this.pListData.actDefId
				if (_this.pListData && curActDefId) {
					 _this.nodeDetailLoading = true
				    getInfoByType(_this.pButton, curActDefId).then(res => {
						let nodeDetail = {}
						res.data.forEach(item => {
							nodeDetail[item.code] = true
						})
						_this.nodeDetail = nodeDetail
						_this.nodeDetailList = res.data
					}).finally(()=>{
					  _this.nodeDetailLoading = false
					  _this.initStatus()
					});
				}
			},
			initStatus() {
				let _this = this
				if (!_this.detailLoading&&!_this.nodeDetailLoading&&!_this.loading) {
					_this.editStatus = _this.nodeShow('bianji') && _this.workflowStatus
					_this.submitLabel = _this.nodeShow('pizhun') ? _this.$t(`file_handle.change_approve`) : _this.$t(
						`file_handle.change_auditing`)
					_this.jointReviewRedirect();
					_this.getWorkflowParams();
				}
			},
			getExtAttributeModel() {
				let _this = this
				let procDefId = _this.pListData.procDefId
				let curActDefId = _this.pListData.curActDefId || _this.pListData.actDefId
				if (procDefId && curActDefId) {
					_this.getNodeDetailInfo()
					_this.loading = true
					getExtAttributeModel(
						procDefId,
						curActDefId
					).then(async (res) => {
						let kuozhanshuju = {}
						res.data.forEach(item => {
							kuozhanshuju[item.objKey] = item.objValue
						})
						_this.kuozhanshuju = kuozhanshuju;
					}).finally(() => {
						_this.loading = false
						_this.initStatus()
					});
				} else {
					_this.kuozhanshuju = {}
					_this.loading = false
				}
			},
      async getWorkflowParams(){
        let _this = this
        //设定流程默认执行人
        let defaultStaff = []
        if (_this.formData.presetUserList.length > 0) {
          defaultStaff.push(...JSON.parse(JSON.stringify(_this.formData.presetUserList)))
        }
        if (_this.nodeShow('cdxmd') && _this.formData.batch) {
          // 文件类型设置中是否设置了 需要谁驳回就再只发送给驳回的人
          let funCondition = _this.nodeFunCondition('cdxmd')
          if (funCondition && funCondition.validate) {
            //查询本次驳回有哪些人员
            let res = await listWorkflowLog({
              batch: _this.formData.batch,
              nextDefId: _this.pListData.curActDefId,
              havaDetail: true
            })
            let nodeCode = ""
            let users = []
            res.rows.forEach(item => {
              nodeCode = item.actDefId
              users.push({
                userName: item.sender,
                nickName: item.nickName,
                deptId: item.senderDeptId,
                deptName: item.deptName
              })
            })
            if (defaultStaff.length > 0) {
              let staff = defaultStaff.find(item => item.nodeCode = nodeCode)
              if (staff) {
                staff.users = JSON.stringify(users)
              }
            } else {
              defaultStaff.push({
                nodeCode: nodeCode,
                users: JSON.stringify(users)
              })
            }
          }
        }
        _this.defaultStaff = defaultStaff
        // _this.searchQuery.step = _this.formData.step?_this.formData.step:0
        // _this.searchQuery.isTrain = _this.formData.yNTrain
        let hideNodeCode = []
        //下一环节未预选人员隐藏
        if (_this.nodeShow('xyhjwyxryyc')) {
          let funCondition = _this.nodeFunCondition('xyhjwyxryyc')
          if (funCondition && funCondition.nodeCode) {
            let length = funCondition.nodeCode.length
            //下一环节隐藏范围 都隐藏
            hideNodeCode = funCondition.nodeCode
            //过滤有预选人员的环节
            defaultStaff.forEach(item => {
              if (item.users) {
                let users = JSON.parse(item.users)
                if (hideNodeCode.includes(item.nodeCode) && users && users
                    .length > 0) {
                  hideNodeCode = hideNodeCode.filter(code => code !== item
                      .nodeCode)
                }
              }
            })
            //配置了反向节点 隐藏范围环节内都没预选人员 过滤掉反向节点
            if (funCondition.neNodeCode && hideNodeCode.length === length) {
              hideNodeCode = hideNodeCode.filter(code => !funCondition.neNodeCode
                  .includes(
                      code))
            }
            //填写了限定值 只能显示最多限定的数量
            if (funCondition.limitValue) {
              let limitValue = Number(funCondition.limitValue)
              //总数-隐藏数=显示数 显示数>限定数量
              if (!isNaN(limitValue) && (length - hideNodeCode.length) > limitValue) {
                //倒叙再插回去
                let reverse = funCondition.nodeCode.reverse()
                for (let item of reverse) {
                  if (!hideNodeCode.includes(item)) {
                    hideNodeCode.push(item)
                  }
                  if ((length - hideNodeCode.length) <= limitValue) {
                    break
                  }
                }
              }
            }
            if (funCondition.validate) {
              //验证开启 配置了反向节点 隐藏范围环节内都有预选人员 增加反向节点
              if (funCondition.neNodeCode && hideNodeCode.length !== length &&
                  hideNodeCode
                      .length ===
                  0) {
                defaultStaff.forEach(item => {
                  if (funCondition.neNodeCode.includes(item.nodeCode)) {
                    hideNodeCode.push(item.nodeCode)
                  }
                })
              }
            }
          }
        }
        //隐藏环节列表
        _this.hideNodeCode = hideNodeCode
        _this.$nextTick(() => {
			if (_this.approvalStatus) {
				_this.$refs.approvalBox.init()
			} else {
				_this.$refs.prochild.init(_this.pListData)
			}
		})
      },
			attributeModel(val) {
				if (this.kuozhanshuju && this.kuozhanshuju !== {}) {
					let obj = this.kuozhanshuju[val]
					return obj ? obj === 'true' : false
				} else {
					return false
				}
			},
			// 提交
			async submitForm() {
				this.$refs.form1.validate().then(() => {
					uni.showLoading({
						mask: true
					});
				}).then(async () => {
					let _this = this
					// 首先页签调整为 信息内容
					_this.activeName = 'info'
					if (!_this.procDefKey) {
						return Promise.reject(this.$t(`doc.this_dept_no_process_setting`))
					}
					// 校验原由与结论是否录入
					if (_this.nodeShow('jielun')) {
						if (_this.formData.itemList.some(item => !item.reviewAction)) {
							return Promise.reject(_this.submitLabel + _this.$t(
								`doc.this_dept_pls_select_review_conclusion`))
						}
					}
					//审核
					if (_this.nodeShow('shenhe') || _this.nodeShow('pizhun')) {
						if (_this.approvalStatus) {
							_this.formSubmit = _this.$refs.approvalBox.formSubmit
						}
						if (_this.formSubmit.pass===undefined) {
							return Promise.reject(_this.submitLabel + _this.$t(`file_handle.change_result_not_null`))
						}
						if (!_this.formSubmit.pass&&_this.formSubmit.summary.trim() == '') {
							return Promise.reject(_this.$t(`doc.this_dept_pls_fill`)+_this.submitLabel+_this.$t(`doc.this_dept_comments`))
						}
					}

				}).then(() => this.handleWorkflowSubmit()).then(() => {
					uni.hideLoading();
				}).catch((err) => {
					uni.hideLoading();
					if (Array.isArray(err)) {
						err = 'Form item not empty'
					}
					this.$refs.uToast.show({
						type: 'error',
						duration: 5000,
						icon: false,
						message: err
					})

				})
			},

			//提交表单和流程数据
			handleWorkflowSubmit(invokeFrom) {
				let _this = this
				let formData = JSON.parse(JSON.stringify(_this.formData))
				let wf_receivers = [];
				let wf_nextActDefId = null,
					wf_nextActDefName = null
				let prochild = _this.approvalStatus? _this.$refs.approvalBox:_this.$refs.prochild
				if (prochild.receiveUserList.length < 1 && prochild.nextData.actDefType !==
					'endEvent') {
					return Promise.reject(this.$t(`doc.this_dept_select_user_alert`))
				}
				prochild.receiveUserList.forEach((element) => {
					wf_receivers.push({
						receiveUserId: element.id,
						receiveUserOrgId: element.parentId,
					});
				});
				wf_nextActDefId = prochild.nextData.actDefId;
				wf_nextActDefName = prochild.nextData.actDefName;

				// 显示加载中
				_this.flowStepLoading = true
				_this.detailLoading = true
				if (_this.pListData && _this.pListData.procInstId) {
					//流程执行参数
					formData.bpmClientInputModel = {
						model: {
							wf_procDefKey: _this.procDefKey,
							wf_procDefId: _this.pListData.procDefId,
							wf_procTitle: _this.formData.applyTitle,
							wf_curActInstId: _this.pListData.curActInstId,
							wf_sendUserId: _this.userInfo.userName,
							wf_sendUserOrgId: _this.userInfo.deptId,
							wf_receivers: wf_receivers,
							wf_nextActDefId: wf_nextActDefId,
							wf_curComment: _this.formSubmit.summary,
							wf_curActDefName: _this.pListData.curActDefName,
							wf_curActDefId: _this.pListData.actDefId,
							wf_nextActDefName: wf_nextActDefName,
						},
						order: _this.pListData.actDefOrder,
						review: _this.nodeShow('shenhe') || _this.nodeShow('pizhun'),
						applyStatus: _this.formSubmit.pass,
						type: formData.type
					};
				} else {
					//创建流程参数
					formData.bpmClientInputModel = {
						type: formData.type,
						model: {
							wf_procTitle: _this.formData.applyTitle,
							wf_nextActDefId: wf_nextActDefId,
							wf_procDefId: _this.pListData.procDefId,
							wf_procDefKey: _this.procDefKey,
							wf_sendUserId: _this.userInfo.userName,
							wf_sendUserOrgId: _this.userInfo.deptId,
							wf_receivers: wf_receivers,
							wf_curActDefName: _this.pListData.actDefName,
							wf_curActDefId: _this.pListData.actDefId,
							wf_nextActDefName: wf_nextActDefName,
						},
						order: _this.pListData.actDefOrder,
						review: _this.nodeShow('shenhe') || _this.nodeShow('pizhun'),
						applyStatus: _this.formSubmit.pass,
					};
				}
				if (prochild.nextData.actDefType==='endEvent') {
					//办结
					formData.recordStatus = 'done'
					formData.bpmClientInputModel.jointReview = false
				} else {
					//进行中
					formData.recordStatus = 'doing'
					formData.bpmClientInputModel.jointReview = prochild.nextData.multi
				}
				if (_this.nodeShow('hscdx')) {
					formData.bpmClientInputModel.batch = formData.batch
					formData.bpmClientInputModel.redirectDefId = _this.redirectDefId
					formData.bpmClientInputModel.redirectReceivers = _this.redirectReceivers
					if (_this.redirectOrder) {
						formData.bpmClientInputModel.order = _this.redirectOrder
					}
				}
				formData.editStatus = _this.editStatus || _this.nodeShow('jielun')
				formData.presetUserEdit = _this.nodeShow('top_btn_preset_user')
				return addReviewApply(formData).then((res) => {
					if (res.code === 200) {
						this.$refs.uToast.show({
							type: 'success',
							icon: false,
							message: this.$t(`doc.this_dept_process_sub_succ`),
							duration: 2000, //显示时间, 毫秒。设为 0 则不会自动关闭，建议1200
							complete: () => {
								_this.close();
							}
						})
					}
				});
			},
			getSettingDocClassTreeseList() {
				let query = {
					classStatus: "1",
					dataType: this.formData.dataType
				}
				if (this.formData.classType === this.classTypeForeign) {
					query.neClassType = undefined
					query.classType = this.classTypeForeign
				} else {
					query.neClassType = this.classTypeForeign
					query.classType = undefined
				}
				settingDocClassList(query).then(
					(response) => {
						this.classLevelOptions = [];
						// 不展示分类：外来文件以及子类
						let res = response.rows
						response.rows.forEach((element, index) => {
							response.rows[index].children = [];
						});
						this.docClassList = JSON.parse(JSON.stringify(res))
						this.classLevelOptions = this.handleTree(res, "id", "parentClassId");
					}
				);
			},
			formatterDocClass(cellValue) {
				let _this = this
				let item = _this.docClassList.find(item => item.id === cellValue)
				return item ? item.className : cellValue
			},
			openMonitor() {
				uni.$u.route('/pages/workflowList/monitor?procInstId=' + this.procInstId)
			},
      close() {
        uni.switchTab({
          url: '/pages/index'
        });
      },
		},
	};
</script>
<style scoped>
	.document_change_add {
		.fujian .el-textarea__inner {
			border: 0 solid #dcdfe6;
			padding: 0;
		}
	}
</style>
