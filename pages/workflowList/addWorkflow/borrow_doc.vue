<template>
	<view class="u-page">
		<!-- :bgColor="bgColor"  -->
		<!-- 文件借阅 -->
		<page-header :procInstId="procInstId" :title="$t(`doc.this_dept_file_borrow`)">
			<view>
				<u--form labelPosition="left" labelWidth="90" ref="form1" :model="formData" :rules="rules">
					<view class="white-card">
						<view class="card-head">
							<!-- 申请信息 -->
							<view class="head-title">{{ $t(`doc.this_dept_appli_info`) }}</view>
						</view>
						<!-- 标题 -->
						<u-form-item :label="$t(`doc.this_dept_title`)" borderBottom required prop="applyTitle">
							{{formData.applyTitle}}
						</u-form-item>
						<!-- 申请部门 -->
						<u-form-item :label="$t(`doc.this_dept_appli_dept`)" borderBottom>
							{{formData.deptName}}
						</u-form-item>
						<!-- 申请人 -->
						<u-form-item :label="$t(`doc.this_dept_claimant`)" borderBottom>
							{{formData.nickName}}
						</u-form-item>
						<!-- 申请时间 -->
						<u-form-item :label="$t(`doc.this_dept_appli_date`)" borderBottom>
							{{formData.applyTime}}
						</u-form-item>
						<!-- 申请原因 -->
						<u-form-item :label="$t(`doc.this_dept_appli_reason`)" borderBottom required prop="reason">
							{{formData.reason}}
						</u-form-item>
					</view>
					<view class="white-card ft-card">
						<view class="card-head">
							<view class="head-title">{{ $t(`doc.company_dept_borrow_info`) }}</view>
						</view><!--card-head卡片标题-->

						<view class="ctr" v-for="(item,index) in formData.itemList" :key="index">
							<!-- 文件类型 -->
							<u-form-item :label="$t(`doc.this_dept_file_type`)" borderBottom prop="docClass">
								{{formatterDocClass(item.docClass)}}
							</u-form-item>
							<!-- 文件名称 -->
							<u-form-item :label="$t(`doc.this_dept_file_names`)" borderBottom prop="docName">
								{{formatterDocClass(item.docName)}}
							</u-form-item>
							<!-- 文件编号 -->
							<u-form-item :label="$t(`doc.this_dept_file_codes`)" borderBottom prop="docId">
								{{item.docId}}
							</u-form-item>
							<!-- 文件版本 -->
							<u-form-item :label="$t(`doc.this_dept_file_versions2`)" borderBottom prop="versionValue">
								{{item.versionValue}}
							</u-form-item>
							<!-- 编制部门 -->
							<u-form-item :label="$t(`doc.this_dept_staffing_dept`)" borderBottom prop="deptName">
								{{item.deptName}}
							</u-form-item>
							<!-- 编制人 -->
							<u-form-item :label="$t(`doc.this_dept_staffs`)" borderBottom prop="nickName">
								{{item.nickName}}
							</u-form-item>
							<!-- 借阅人 -->
							<u-form-item :label="$t(`doc.company_dept_borrow_person`)" borderBottom
								prop="borrowNickName">
								{{item.borrowNickName}}
							</u-form-item>
							<!-- 是否长期有效 -->
							<u-form-item :label="$t(`doc.company_dept_whether`)" v-if="editStatus" borderBottom
								prop="isForever">
								<dict-tag :options="dict.type.sys_yes_no" :value="item.isForever" />
							</u-form-item>
							<!-- 借阅期限(天) -->
							<u-form-item :label="$t(`doc.company_dept_longterm_effc`)" borderBottom prop="timeLimit">
								<view class="text" v-if="item.isForever === 'N'">
									{{item.timeLimit}}
								</view>
								<view class="text" v-else>{{$t(`doc.company_dept_long_effective`)}}</view>
							</u-form-item>

							<template v-if="!workflowStatus||attributeModel('jielun')||attributeModel('fabu')">
								<!-- 结论 -->
								<u-form-item :label="$t(`file_handle.change_result`)" borderBottom prop="borrowAction">
									<my-select :data="borrowOptions" v-model="item.borrowAction"
										:disabled="!(attributeModel('jielun')&&workflowStatus&&validateUser(item.presetUser))"></my-select>
								</u-form-item>
								<!-- 原由-->
								<u-form-item :label="$t(`doc.company_dept_reason`)" borderBottom prop="reason">
									<u--input
										:disabled="!(attributeModel('jielun')&&workflowStatus&&validateUser(item.presetUser))"
										v-model="item.reason"></u--input>
								</u-form-item>
							</template>
						</view>
					</view><!--white-card 白色卡片-->
				</u--form>
				<approval-box v-if="workflowStatus&&approvalStatus" ref="approvalBox"
								:submitLabel="submitLabel" :selected="attributeModel('default_selected')"
							    :order="parseInt(order)" :searchQuery="searchQuery"
							    :defaultStaff="defaultStaff" :pListData="pListData"
								:status="(attributeModel('shenhe')||attributeModel('pizhun'))"></approval-box>
				<view class="white-card" v-if="(attributeModel('shenhe')||attributeModel('pizhun'))&&workflowStatus&&!approvalStatus">
					<view class="card-head">
						<view class="head-title">{{submitLabel}}</view>
					</view>
					<u--form labelPosition="left" labelWidth='30%' :model="formSubmit" :rules="rules"
						ref="validateForm">
						<!-- 结论 -->
						<u-form-item required :label="submitLabel+$t(`doc.this_dept_conclusion`) " prop="pass"
							borderBottom>
							<u-radio-group v-model="formSubmit.pass" @change="commentItemSelect" placement="row">
								<u-radio style="margin-right: 10px;" v-for="(item,index) in passoptions" :key="index"
									:name="item.value" :label="item.label"></u-radio>
							</u-radio-group>
						</u-form-item>
						<!-- 意见 -->
						<u-form-item required :label="submitLabel + $t(`doc.this_dept_comments`) " borderBottom
							class="label-top">
							<u--textarea v-model="formSubmit.summary"
								:placeholder="$t(`doc.this_dept_insert`)+submitLabel+$t(`doc.this_dept_comments`)"></u--textarea>
						</u-form-item>
					</u--form>
				</view>
				<processcode ref="prochild" v-if="workflowStatus&&!approvalStatus" :searchQuery="searchQuery" :order = "order"
					:defaultStaff="defaultStaff" :selected="attributeModel('default_selected')"></processcode>
				<u-toast ref="uToast"></u-toast>
				<view class="foot-btn">
					<u-row>
						<u-col span="6">
							<u-button type="primary" v-if="!editStatus&&workflowStatus"
								:text="$t('doc.this_dept_annex')" @click="submitForm"></u-button>
						</u-col>
						<u-col span="6">
							<u-button type="success" :text="$t('doc.this_dept_process_monitor')"
								@click="openMonitor"></u-button>
						</u-col>
					</u-row>
				</view>
			</view>
		</page-header>
	</view>

</template>
<script>
	import {
		settingDocClassList
	} from "@/api/file_settings/type_settings";
	import {
		settingDocClassId
	} from "@/api/file_settings/type_settings";
	import {
		isExistByName
	} from "@/api/document_account/standard";
	import {
		workflowprocesskey,
		getStartActdef,
		getExtAttributeModel,
		procInstInfoAndStatus,
	} from '@/api/my_business/workflow'
	// PDF本地文件预览
	import {
		getWorkflowApplyLog
	} from '@/api/my_business/workflowApplyLog'
	import {
		addBorrowApply
	} from '@/api/file_processing/borrowApply'
	import {
		addExtraApplyByBpmnId,
		getBorrowApplyByBpmnId,
		listBorrowApplyItem
	} from "../../../api/my_business/modifyApplyTrain.js";
	import { getInfo } from '@/api/setting/docClassFlowNodeDetail'
	import processcode from "@/pages/common/processcode/index.vue";
	import PageHeader from "./add_import/pageHeader.vue";
	import approvalBox from './add_import/approvalBox.vue';
	export default {
		dicts: ["business_status", "sys_yes_no"],
		name: "Add_doc",
		props: ["dataType", 'data'],
		components: {
			processcode,
			PageHeader,
			approvalBox
		},
		data() {

			return {
				approvalStatus: true,
				order: 0,
				defaultStaff: undefined,
				searchQuery: {},
				open: false,
				docClassList: [],
				appendixsList: [],
				remarkfileList: [],
				changeFactor: [],
				actionTypeoptions: [{
						value: "keep",
						label: this.$t(`doc.this_dept_file_status_quo`)
					},
					{
						value: "update",
						label: this.$t(`file_set.type_revise`)
					},
					{
						value: "disuse",
						label: this.$t(`file_set.type_repeal`)
					},
				],
				submitLabel: this.$t('doc.this_dept_annex'),
				isProject: false,
				docIdData: {},
				jiluliData: [],
				shenchenbianhao: false,
				borrowOptions: [
					{ value: 'pass', label: this.$t(`doc.this_dept_pass`) },
					{ value: 'un_pass', label: this.$t(`doc.this_dept_not_pass`) },
				],
				passoptions: [
					{ value: true, label: this.$t(`doc.this_dept_pass`) },
					{ value: false, label: this.$t(`doc.this_dept_not_pass`) },
				],
				formSubmit: { summary: "", actionType: "", pass: undefined },
				pButton: 'borrow',
				isSummary: false,
				projectList: [],
				project: {
					id: '',
					name: ''
				},
				activeName: "info",
				nodeDetail: [],
				procDefKey: undefined,
				processData: {},
				viewId: "",
				userInfo: this.$store.getters.user,
				viewShow: false,
				active: 4,
				activeIndex: "1",
				uploadType: ["doc", "docx", "ppt", "xlsx", "pdf", "jpg", "png"],
				monitorDrawerVisible: false,
				formData: {
					id: undefined,
					applyTitle: undefined,
					deptId: undefined,
					userName: undefined,
					reason: undefined,
					status: undefined,
					createTime: undefined,
					itemList: []
				},
				rules: {
					applyTitle: [{
						required: true,
						message: this.$t(`doc.this_dept_insert_title`),
						trigger: "blur"
					}, ],
					pass: [{
						required: true,
						message: this.$t(`doc.this_dept_pls_select`),
						trigger: "blur"
					}, ],
					reason: [{
						required: true,
						message: this.$t(`doc.this_dept_insert_appli_reason`),
						trigger: "blur"
					}, ],
				},
				kuozhanshuju: {},
				field117Action: "",
				action: "/dms-admin/process/file/local_upload",
				appendixesfileList: [],
				standardDocfileList: [],
				menuitem: "1",
				summary: "",
				pListData: {},
				editStatus: false,
				workflowStatus: false,
				dialogVisible: false,
				processcodeData: {},
				processInstanceModel: {},
				disabled: false,

				loading: false,
				detailLoading: true,
				flowStepLoading: false
			};
		},


		async onLoad(option) {
			let row = option
			this.procInstId = row.procInstId
			this.workflowStatus = row.status == '1'
			this.status = row.status
			this.formData.type = row.type
			if (row.order) {
				this.order = row.order
		    }
			if (row.preChangeCode) {
				let res = await getWorkflowApplyLog(row.preChangeCode)
				this.procInstId = res.data.procInstId
				this.procInstInfoAndStatus(this.procInstId)
				this.getDetail(this.procInstId)
			} else {
				this.procInstInfoAndStatus(this.procInstId)
				this.getDetail(this.procInstId)
			}
		},
		methods: {
			commentItemSelect(val) {
				this.formSubmit.summary =this.passoptions.find(v=>v.value == val).label
				this.searchQuery.pass = this.formSubmit.pass
				this.$refs.prochild.init(this.pListData)
			},
			procInstInfoAndStatus(procInstId) {
				let _this = this
				uni.showLoading({
					mask: true
				})
				procInstInfoAndStatus(procInstId).then((res) => {
					if (res) {
						_this.procDefKey = res.procDefKey
						_this.pListData = res
					} else {
						_this.pListData = {
							procInstId: procInstId
						}
					}
					_this.getExtAttributeModel()
					if (this.$refs.prochild) {
						this.$refs.prochild.init(_this.pListData)
					}
					uni.hideLoading()
				});
			},
			openMonitor() {
				uni.$u.route('/pages/workflowList/monitor?procInstId=' + this.procInstId)
				//window.open(process.env.VUE_APP_FLOW_IMG_PATH + processInstanceId);
			},
			formatterPassOptions(value) {
				let _this = this
				if (value) {
					let option = _this.actionTypeoptions.find(item => item.value === value)
					return option ? option.label : value
				}
				return ""
			},
			getDetail(procInstId) {
				let _this = this
				_this.detailLoading = true
				getBorrowApplyByBpmnId(procInstId).then(async (res) => {
					let formData = res.data;
					formData.type = _this.formData.type
					let res1 = await listBorrowApplyItem({applyId:formData.id})
					formData.itemList = res1.data
					_this.formData = formData
					_this.getSettingDocClassTreeseList();
				}).then(() => {
					let defaultStaff = []

					if (_this.attributeModel("jlyxr")) {
						//给环节默认人员 现阶段没有配置地方 只能写死某环节
						let nodeCode = 'sid-380E9407-05C5-4FCE-A994-09FCDA23DC1A'
						let users = []
						_this.formData.itemList.forEach(item => {
							if (item.presetUser) {
								let presetUser = JSON.parse(item.presetUser)
								if (users.findIndex(user => user.userName === presetUser
										.userName) <
									0) {
									users.push(presetUser)
								}
							}
						})
						defaultStaff.push({
							nodeCode: nodeCode,
							users: JSON.stringify(users)
						})
					}
					this.defaultStaff = defaultStaff
				}).finally(() => {
					_this.detailLoading = false
					_this.initStatus()
				});
			},
			nodeShow(code) {
				let _this = this
				if (_this.nodeDetail) {
					return !!_this.nodeDetail.find(node => node.code === code)
				} else {
					return false
				}
			},
			// 提交
			async submitForm() {
				let _this = this
				this.$refs.form1.validate().then(() => {
					uni.showLoading({
						mask: true
					})
				}).then(async () => {
					if (!_this.procDefKey) {
						return Promise.reject(this.$t(`doc.this_dept_no_process_setting`))
					}
					//审核
					if (_this.attributeModel('shenhe') || _this.attributeModel('pizhun')) {
						if (_this.approvalStatus) {
							_this.formSubmit = _this.$refs.approvalBox.formSubmit
						}
						if (_this.formSubmit.pass===undefined) {
							return Promise.reject(_this.submitLabel + _this.$t(`file_handle.change_result_not_null`))
						}
						if (!_this.formSubmit.pass&&_this.formSubmit.summary.trim() == '') {
							return Promise.reject(_this.$t(`doc.this_dept_pls_fill`)+_this.submitLabel+_this.$t(`doc.this_dept_comments`))
						}
					}
					// 校验文件借阅详情
					await _this.validate()
				}).then(() => this.handleWorkflowSubmit()).then(() => {
					uni.hideLoading()
				}).catch((err) => {
					uni.hideLoading()
					if (Array.isArray(err)) {
						err = 'Form item not empty'
					}
					console.log("表单校验不成功", err)
					this.$refs.uToast.show({
						type: 'error',
						duration: 5000,
						icon: false,
						message: err
					})
				})
			},
			validate() {
				// 验证是否填写了审核意见
				let _this = this
				if (!_this.formData.itemList || _this.formData.itemList.length < 1) {
					return Promise.reject(_this.$t(`doc.this_dept_select_file`));
				}
				if (_this.editStatus) {
					if (_this.formData.itemList.some(item => !item.borrowUserName)) {
						return Promise.reject(_this.$t(`doc.this_dept_pls_borrow_user`));
					}
					let item = _this.formData.itemList.find(item => !item.presetUser)
					if (item) {
						return Promise.reject(this.$t(`doc.this_dept_staffs`) + "【" + item.nickName + "】" + this.$t(
							`doc.company_dept_text`));
					}
				}
				if (_this.formData.itemList.some(item =>
						!item || item.timeLimit <= 0 || item.timeLimit == undefined
					)) {
					return Promise.reject(_this.$t(`doc.this_dept_pls_borrow_time`));
				}
				if (_this.attributeModel('shenhe') || _this.attributeModel('pizhun')) {
					if (_this.formSubmit.summary.trim() == '') {
						return Promise.reject(_this.$t(`doc.this_dept_pls_fill`) + _this.submitLabel + _this.$t(
							`doc.this_dept_comments`));
					}
				}
				if (_this.attributeModel('jielun')) {
					if (_this.formData.itemList.some(item => !item.borrowAction && _this.validateUser(item.presetUser))) {
						return Promise.reject(_this.$t(`doc.this_dept_pls_borrow_conclusion`));
					}
				}
				return Promise.resolve();
			},
			getExtAttributeModel() {
				let _this = this
				let procDefId = _this.pListData.procDefId
				let curActDefId = _this.pListData.curActDefId || _this.pListData.actDefId
				if (procDefId && curActDefId) {
					getExtAttributeModel(
						procDefId,
						curActDefId
					).then((res) => {
						console.log("扩展属性====>", res);
						let kuozhanshuju = {}
						res.data.forEach(item => {
							kuozhanshuju[item.objKey] = item.objValue
						})
						_this.kuozhanshuju = kuozhanshuju;
					}).finally(() => {
						_this.loading = false
						_this.initStatus()
					});
				} else {
					_this.kuozhanshuju = {}
					_this.loading = false
				}
			},
			async initStatus() {
			  let _this = this
			  if (!_this.detailLoading&&!_this.loading){
				_this.editStatus = _this.attributeModel('bianji') && _this.workflowStatus
				_this.submitLabel = _this.attributeModel('pizhun') ? _this.$t(`file_handle.change_approve`) : _this.attributeModel('shenhe')?_this.$t(`file_handle.change_auditing`):_this.$t('doc.this_dept_annex')
				_this.$nextTick(() => {
					if (_this.approvalStatus) {
						_this.$refs.approvalBox.init()
					} else {
						_this.$refs.prochild.init(_this.pListData)
					}
				})
			  }
			},
			attributeModel(val) {
				if (this.kuozhanshuju && this.kuozhanshuju !== {}) {
					let obj = this.kuozhanshuju[val]
					return obj ? obj === 'true' : false
				} else {
					return false
				}
			},
			handlePreview(fileId) {
				let url = `/pages/pdfPreview/index?fileId=${fileId}`
				uni.$u.route(url)
			},
			//提交表单和流程数据
			handleWorkflowSubmit(invokeFrom) {
				let _this = this
				let formData = uni.$u.deepClone(_this.formData)
				let wf_receivers = [];
				let wf_nextActDefId = null,
					wf_nextActDefName = null
				let prochild = _this.approvalStatus? _this.$refs.approvalBox:_this.$refs.prochild
				if (prochild.receiveUserList.length < 1 && prochild.nextData.actDefType !==
					'endEvent') {
					return Promise.reject(this.$t(`doc.this_dept_select_user_alert`))
				}
				prochild.receiveUserList.forEach((element) => {
					wf_receivers.push({
						receiveUserId: element.id,
						receiveUserOrgId: element.parentId,
					});
				});
				wf_nextActDefId = prochild.nextData.actDefId;
				wf_nextActDefName = prochild.nextData.actDefName;
				if (_this.pListData && _this.pListData.procInstId) {
					//流程执行参数
					formData.bpmClientInputModel = {
						model: {
							wf_procDefKey: _this.procDefKey,
							wf_procDefId: _this.pListData.procDefId,
							wf_procTitle: _this.formData.applyTitle,
							wf_curActInstId: _this.pListData.curActInstId,
							wf_sendUserId: _this.userInfo.userName,
							wf_sendUserOrgId: _this.userInfo.deptId,
							wf_receivers: wf_receivers,
							wf_nextActDefId: wf_nextActDefId,
							wf_curComment: _this.formSubmit.summary,
							wf_curActDefName: _this.pListData.curActDefName,
							wf_curActDefId: _this.pListData.actDefId,
							wf_nextActDefName: wf_nextActDefName,
						},
						review: _this.attributeModel('shenhe') || _this.attributeModel('pizhun'),
						applyStatus: _this.formSubmit.pass || false,
						type: formData.type
					};
				} else {
					//创建流程参数
					formData.bpmClientInputModel = {
						type: formData.type,
						model: {
							wf_procTitle: _this.formData.applyTitle,
							wf_nextActDefId: wf_nextActDefId,
							wf_procDefId: _this.pListData.procDefId,
							wf_procDefKey: _this.procDefKey,
							wf_sendUserId: _this.userInfo.userName,
							wf_sendUserOrgId: _this.userInfo.deptId,
							wf_receivers: wf_receivers,
							wf_curActDefName: _this.pListData.actDefName,
							wf_curActDefId: _this.pListData.actDefId,
							wf_nextActDefName: wf_nextActDefName
						},
						review: _this.attributeModel('shenhe') || _this.attributeModel('pizhun'),
					};
				}
				if (_this.attributeModel('fabu')) {
					//办结
					formData.recordStatus = 'done'
				} else {
					//进行中
					formData.recordStatus = 'doing'
				}
				formData.editStatus = _this.editStatus || _this.attributeModel('jielun')
				return addBorrowApply(formData).then((res) => {
					if (res.code === 200) {
						this.$refs.uToast.show({
							type: 'success',
							icon: false,
							message: this.$t(`doc.this_dept_process_sub_succ`),
							duration: 2000, //显示时间, 毫秒。设为 0 则不会自动关闭，建议1200
							complete: () => {
								_this.close();
							}
						})
					}
				});
			},
			handleSelect(source, index, deptId) {
				let _this = this
				_this.$nextTick(() => {
					_this.$refs.userList.init(source, index, deptId)
				})
			},
			getSettingDocClassTreeseList() {
				settingDocClassList({
					classStatus: "1",
					dataType: this.formData.dataType,
					neClassType: 'foreign'
				}).then(
					(response) => {
						this.docClassList = JSON.parse(JSON.stringify(response.rows))
					}
				);
			},

			formatterDocClass(cellValue) {
				let _this = this
				let item = _this.docClassList.find(item => item.id === cellValue)
				return item ? item.className : cellValue
			},
			validateUser(presetUser) {
				if (presetUser) {
					let user = JSON.parse(presetUser)
					if (user.userName === this.userInfo.userName) {
						return true
					}
				}
				return false
			},
			close() {
				uni.switchTab({
					url: '/pages/index'
				});
			},
		},
	};
</script>
<style scoped>
	.document_change_add {
		.fujian .el-textarea__inner {
			border: 0 solid #dcdfe6;
			padding: 0;
		}
	}
</style>
