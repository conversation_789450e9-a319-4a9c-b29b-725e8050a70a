<template>
	<view class="u-page">
		<!-- 文件补发 -->
		<page-header :procInstId="procInstId" :title="$t(`doc.this_dept_file_reissue`)">
			<view>
				<u--form labelPosition="left" labelWidth="30%" :model="formData" :rules="rules" ref="form1">
					<view class="white-card">
						<view class="card-head">
							<!-- 申请信息 -->
							<view class="head-title">{{$t(`doc.this_dept_appli_info`)}}</view>
						</view>
						<!-- 标题 -->
						<u-form-item :label="$t(`doc.this_dept_title`)" prop="applyTitle" required>
							{{formData.applyTitle}}
						</u-form-item>
						<!-- 申请部门 -->
						<u-form-item :label="$t(`doc.this_dept_appli_dept`)" prop="deptName">
							{{formData.deptName}}
						</u-form-item>
						<!-- 申请人 -->
						<u-form-item :label="$t(`doc.this_dept_claimant`)" prop="nickName">
							{{formData.nickName}}</u-form-item>
						<!-- 申请时间 -->
						<u-form-item :label="$t(`doc.this_dept_appli_date`)" prop="applyTime">
							{{formData.applyTime}}
						</u-form-item>
						<!-- 文件名称 -->
						<u-form-item :label="$t(`doc.this_dept_file_names`)" prop="docName">
							{{formData.docName}}
						</u-form-item>
						<!-- 文件编号 -->
						<u-form-item :label="$t(`doc.this_dept_file_code`)" prop="docId">
							{{formData.docId}}
						</u-form-item>
						<!-- 文件版本 -->
						<u-form-item :label="$t(`doc.this_dept_file_versions`)" prop="versionValue">
							{{formData.versionValue}}
						</u-form-item>
						<!-- 文件类型 -->
						<u-form-item :label="$t(`doc.this_dept_file_type`)" prop="docClass">
							{{formatterDocClass(null,null,formData.docClass,null)}}
						</u-form-item>
						<!-- 申请原因 -->
						<u-form-item :label="$t(`doc.this_dept_appli_reason`)" prop="reason" required>
							{{formData.reason}}
						</u-form-item>
					</view>

					<view class="white-card">
						<view class="card-head">
							<!-- 补发文件信息 -->
							<view class="head-title">{{$t(`doc.this_dept_reissue_file_info`)}}</view>
						</view>
						<view class="ft-card">
							<view class="ctr" v-for="(item,index) in formData.itemList" :key="index">
								<view class="clist">
									<!-- 分发号 -->
									<view class="clum">{{$t(`doc.this_dept_distribute_info`)}}</view>
									<view class="text">{{formatterCode(item)}}</view>
								</view>
								<view class="clist">
									<!-- 签收部门 -->
									<view class="clum">
										{{$t(`doc.this_dept_sign_dept`)}}
									</view>
									<view class="text">{{item.receiveUserDept}}</view>
								</view>
								<view class="clist">
									<!-- 签收人 -->
									<view class="clum">
										{{$t(`doc.this_dept_signatory`)}}
									</view>
									<view class="text">{{item.receiveNickName}}</view>
								</view>
								<view class="clist">
									<!-- 签收时间 -->
									<view class="clum">{{$t(`doc.this_dept_sign_date`)}}</view>
									<view class="text">{{item.receiveTime}}</view>
								</view>
								<view class="clist">
									<!-- 状态 -->
									<view class="clum">{{$t(`doc.this_dept_status`)}}</view>
									<view class="text">{{formatterStatus(item)}}</view>
								</view>

							</view>
						</view>
					</view>

				</u--form>
				<approval-box v-if="workflowStatus&&approvalStatus" ref="approvalBox"
								:submitLabel="submitLabel" :selected="attributeModel('default_selected')"
							    :order="parseInt(order)" :searchQuery="searchQuery"
							    :defaultStaff="defaultStaff" :pListData="pListData"
								:status="(attributeModel('shenhe')||attributeModel('pizhun'))"></approval-box>
				<view class="white-card" v-if="(attributeModel('shenhe')||attributeModel('pizhun'))&&workflowStatus&&!approvalStatus">
					<view class="card-head">
						<view class="head-title">{{submitLabel}}</view>
					</view>
					<u--form labelPosition="left" labelWidth="30%" :model="formSubmit" :rules="rules"
						ref="validateForm">
						<!-- 结论 -->
						<u-form-item required :label="submitLabel+$t(`doc.this_dept_conclusion`) " prop="pass"
							borderBottom>
							<u-radio-group v-model="formSubmit.pass" @change="commentItemSelect" placement="row">
								<u-radio style="margin-right: 10px;" v-for="(item,index) in passoptions" :key="index"
									:name="item.value" :label="item.label"></u-radio>
							</u-radio-group>
						</u-form-item>
						<!-- 意见 -->
						<u-form-item required :label="submitLabel + $t(`doc.this_dept_comments`)" prop="summary"
							borderBottomclass="label-top">
							<u--textarea v-model="formSubmit.summary"
								:placeholder="$t(`doc.this_dept_insert`)+submitLabel+$t(`doc.this_dept_comments`)"></u--textarea>
						</u-form-item>
					</u--form>
				</view>
				<processcode v-if="workflowStatus&&!approvalStatus" ref="prochild" :searchQuery="searchQuery" :order = "order"
					:defaultStaff="defaultStaff" :selected="attributeModel('default_selected')"></processcode>

				<view class="foot-btn">
					<u-row>
						<u-col span="6">
							<u-button type="primary" v-if="!editStatus&&workflowStatus"
								:text="$t('doc.this_dept_annex')" @click="submitForm"></u-button>
						</u-col>
						<u-col span="6">
							<u-button type="success" :text="$t('doc.this_dept_process_monitor')"
								@click="openMonitor"></u-button>
						</u-col>
					</u-row>
				</view>

			</view>
		</page-header>
		<u-toast ref="uToast"></u-toast>
	</view>
</template>

<script>
	import {
		settingDocClassId,
		settingDocClassList
	} from "@/api/file_settings/type_settings";
	import {
		workflowprocesskey,
		getStartActdef,
		getExtAttributeModel,
		procInstInfoAndStatus,

	} from '@/api/my_business/workflow'
	// PDF本地文件预览
	import {
		getWorkflowApplyLog
	} from '@/api/my_business/workflowApplyLog'
	import {
		addReissueApply,
		getReissueApplyByBpmnId
	} from '@/api/file_processing/reissueApply.js'
	import {
		listReissueApplyItem
	} from "../../../api/my_business/modifyApplyTrain";
	import {
		listPresetUser
	} from "../../../api/setting/presetUser";
	import {
		getInfo
	} from "@/api/setting/docClassFlowNodeDetail";
	import processcode from "@/pages/common/processcode/index.vue";
	import PageHeader from "./add_import/pageHeader.vue";
	import approvalBox from './add_import/approvalBox.vue';
	export default {
		dicts: ['sys_yes_no'],
		name: "Reissue_doc",
		components: {
			processcode,
			PageHeader,
			approvalBox
		},
		data() {
			return {
				approvalStatus: true,
				order: 0,
				defaultStaff: undefined,
				pButton: 'reissue',
				drawerShow: false,
				searchQuery: {},
				deptList: [],
				companyList: [],
				deptOptions: [],
				docClassList: [],
				submitLabel: this.$t('doc.this_dept_annex'),
				passoptions: [
					{ value: true, label: this.$t(`doc.this_dept_pass`) },
					{ value: false, label: this.$t(`doc.this_dept_not_pass`) },
				],
				formSubmit: { summary: "", actionType: "", pass: undefined },
				isSummary: false,
				activeName: "info",
				nodeDetail: [],
				procDefKey: undefined,
				viewId: "",
				userInfo: this.$store.getters.user,
				viewShow: false,
				monitorDrawerVisible: false,
				formData: {
					id: undefined,
					applyTitle: undefined,
					deptId: undefined,
					userName: undefined,
					deptName: undefined,
					nickName: undefined,
					versionId: undefined,
					docId: undefined,
					docName: undefined,
					versionValue: undefined,
					docClass: undefined,
					reason: undefined,
					status: undefined,
					applyTime: undefined,
					isPuttingOut: undefined,
					itemList: []
				},
				kuozhanshuju: {},
				pListData: {},
				editStatus: false,
				workflowStatus: false,
				dialogVisible: false,
				loading: false,
				detailLoading: false,
				flowStepLoading: false,
				rules: {
					isTrain: [{
						required: true,
						message: this.$t(`doc.this_dept_select_is_train`),
						trigger: "blur,change"
					}, ],
					isDistribute: [{
						required: true,
						message: this.$t(`doc.this_dept_select_is_distribute`),
						trigger: "blur,change"
					}, ],
					isPuttingOut: [{
						required: true,
						message: this.$t(`doc.this_dept_select_whether_outgoing`),
						trigger: "blur"
					}, ],
					applyTitle: [{
						required: true,
						message: this.$t(`doc.this_dept_insert_title`),
						trigger: "blur"
					}, ],
					pass: [{
						required: true,
						message: this.$t(`doc.this_dept_pls_select`),
						trigger: "blur"
					}, ],
					reason: [{
						required: true,
						message: this.$t(`doc.this_dept_insert_appli_reason`),
						trigger: "blur"
					}, ],
				},
			};
		},
		async onLoad(option) {
			let row = option
			this.procInstId = row.procInstId
			this.workflowStatus = row.status == '1'
			this.status = row.status
			this.type = row.type
			this.formData.type = row.type
			if (row.order) {
				this.order = row.order
			}
			if (row.preChangeCode) {
				let res = await getWorkflowApplyLog(row.preChangeCode)
				this.procInstId = res.data.procInstId
				this.procInstInfoAndStatus(this.procInstId)
				this.getDetail(this.procInstId)
			}else{
				this.procInstInfoAndStatus(this.procInstId)
				this.getDetail(this.procInstId)

			}		},
		methods: {
			commentItemSelect(val) {
				this.formSubmit.summary =this.passoptions.find(v=>v.value == val).label
				this.searchQuery.pass = this.formSubmit.pass
				this.$refs.prochild.init(this.pListData)
			},
			procInstInfoAndStatus(procInstId) {
				let _this = this
				uni.showLoading({
					mask: true
				})
				procInstInfoAndStatus(procInstId).then((res) => {
					if (res) {
						_this.procDefKey = res.procDefKey
						_this.pListData = res
					} else {
						_this.pListData = {
							procInstId: procInstId
						}
					}
					_this.getExtAttributeModel()
					if (this.$refs.prochild) {
						this.$refs.prochild.init(_this.pListData)
					}
					uni.hideLoading()
				});
			},
			handleExport2() {
				this.$refs.pdfView.init()
			},
			formatterCode(row) {
				if (row.code < 10) {
					return '0' + row.code;
				}
				return row.code;
			},
			formatterStatus(row) {
				if (row.status === 'lost') {
					return this.$t(`doc.this_dept_loss`)
				} else if (row.status === 'recovery') {
					return this.$t(`file_handle.print_recovered`)
				} else {
					return this.$t(`file_handle.print_unrecover`)
				}
			},

			getDetail(procInstId) {
				let _this = this
				_this.detailLoading = true
				getReissueApplyByBpmnId(procInstId).then(async (res) => {
					let formData = res.data;
					formData.type = _this.formData.type
					_this.formData = formData
					_this.getSettingDocClassTreeseList();
					_this.getItemList(formData.id)
					let res3 = await listPresetUser({
						bizId: formData.id
					})
					formData.presetUserList = res3.data
				}).finally(() => {
					_this.detailLoading = false
				});
			},
			getItemList(applyId) {
				listReissueApplyItem({
					applyId: applyId
				}).then(res => {
					this.$set(this.formData, 'itemList', res.data)
				})
			},
			rest() {
				let _this = this
				_this.activeName = "info"
				_this.formData = {
					id: undefined,
					applyTitle: undefined,
					deptId: this.userInfo.deptId,
					userName: this.userInfo.userName,
					deptName: this.userInfo.dept.deptName,
					nickName: this.userInfo.nickName,
					versionId: undefined,
					docId: undefined,
					docName: undefined,
					versionValue: undefined,
					docClass: undefined,
					reason: undefined,
					status: undefined,
					applyTime: _this.parseTime(new Date()),
					isPuttingOut: undefined,
					itemList: []
				}
			},
			nodeShow(code) {
				let _this = this
				if (_this.nodeDetail) {
					return !!_this.nodeDetail.find(node => node.code === code)
				} else {
					return false
				}
			},
			getWorkflowprocesskey() {
				let _this = this
				_this.loading = true
				_this.pListData = {}
				if (_this.procDefKey) {
					workflowprocesskey(_this.procDefKey).then((data) => {
						getStartActdef(data.data.procDefId).then((res) => {
							_this.pListData = res.data;
							this.getExtAttributeModel()
						});
					});
				} else {
					_this.$message.warning(_this.$t(`doc.this_dept_no_process_setting`));
				}
			},
			getExtAttributeModel() {
				let _this = this
				let procDefId = _this.pListData.procDefId
				let curActDefId = _this.pListData.curActDefId || _this.pListData.actDefId
				if (procDefId && curActDefId) {
					getExtAttributeModel(
						procDefId,
						curActDefId
					).then((res) => {
						console.log("扩展属性====>", res);
						let kuozhanshuju = {}
						res.data.forEach(item => {
							kuozhanshuju[item.objKey] = item.objValue
						})
						_this.kuozhanshuju = kuozhanshuju;
						_this.editStatus = _this.attributeModel('bianji') && _this.workflowStatus
						_this.submitLabel = _this.attributeModel('pizhun') ? this.$t(`file_handle.change_approve`) : this.$t(`file_handle.change_auditing`)
						if (_this.approvalStatus) {
							_this.$refs.approvalBox.init()
						} else {
							_this.$refs.prochild.init(_this.pListData)
						}
					}).finally(() => {
						_this.loading = false
					});
				} else {
					_this.kuozhanshuju = {}
					_this.loading = false
				}
			},
			attributeModel(val) {
				if (this.kuozhanshuju && this.kuozhanshuju !== {}) {
					let obj = this.kuozhanshuju[val]
					return obj ? obj === 'true' : false
				} else {
					return false
				}
			},
			submitForm() {
				let _this = this

				this.$refs.form1.validate().then(() => {
					uni.showLoading({
						mask: true
					});
				}).then(async () => {
					let _this = this
					if (!_this.procDefKey) {
						return Promise.reject(_this.$t(`doc.this_dept_no_process_setting`))
					}
					let dialogVisible = true
					//审核
					if (_this.attributeModel('shenhe') || _this.attributeModel('pizhun')) {
						if (_this.approvalStatus) {
							_this.formSubmit = _this.$refs.approvalBox.formSubmit
						}
						if (_this.formSubmit.pass===undefined) {
							return Promise.reject(_this.submitLabel + _this.$t(`file_handle.change_result_not_null`))
						}
						if (!_this.formSubmit.pass&&_this.formSubmit.summary.trim() == '') {
							return Promise.reject(_this.$t(`doc.this_dept_pls_fill`)+_this.submitLabel+_this.$t(`doc.this_dept_comments`))
						}
					}
					_this.searchQuery.isPuttingOut = _this.formData.isPuttingOut
				}).then(() => this.handleWorkflowSubmit()).then(() => {
					uni.hideLoading();
				}).catch((err) => {
					uni.hideLoading();
					if (Array.isArray(err)) {
						err = 'Form item not empty'
					}
					console.log("表单校验不成功", err)
					this.$refs.uToast.show({
						type: 'error',
						duration: 5000,
						icon: false,
						message: err
					})

				})
			},
			//提交表单和流程数据
			handleWorkflowSubmit(invokeFrom) {
				let _this = this
				let formData = JSON.parse(JSON.stringify(_this.formData))
				let wf_receivers = [];
				let wf_nextActDefId = null
				let wf_nextActDefName = null
				let prochild = _this.approvalStatus? _this.$refs.approvalBox:_this.$refs.prochild
				if (prochild.receiveUserList.length < 1 && prochild.nextData.actDefType !==
					'endEvent') {
					return Promise.reject(_this.$t(`doc.this_dept_select_user_alert`))
				}
				prochild.receiveUserList.forEach((element) => {
					wf_receivers.push({
						receiveUserId: element.id,
						receiveUserOrgId: element.parentId,
					});
				});
				wf_nextActDefId = prochild.nextData.actDefId;
				wf_nextActDefName = prochild.nextData.actDefName;
				// 显示加载中
				_this.flowStepLoading = true
				_this.detailLoading = true
				if (_this.pListData && _this.pListData.procInstId) {
					//流程执行参数
					formData.bpmClientInputModel = {
						model: {
							wf_procDefKey: _this.procDefKey,
							wf_procDefId: _this.pListData.procDefId,
							wf_procTitle: _this.formData.applyTitle,
							wf_curActInstId: _this.pListData.curActInstId,
							wf_sendUserId: _this.userInfo.userName,
							wf_sendUserOrgId: _this.userInfo.deptId,
							wf_receivers: wf_receivers,
							wf_nextActDefId: wf_nextActDefId,
							wf_curComment: _this.formSubmit.summary,
							wf_curActDefId: _this.pListData.curActDefId,
							wf_curActDefName: _this.pListData.curActDefName,
							wf_nextActDefName: wf_nextActDefName,
						},
						order: _this.pListData.actDefOrder,
						applyStatus: _this.formSubmit.pass,
						review: _this.attributeModel('shenhe')||_this.attributeModel('pizhun'),
						type: formData.type
					};
				} else {
					//创建流程参数
					formData.bpmClientInputModel = {
						type: formData.type,
						model: {
							wf_procTitle: _this.formData.applyTitle,
							wf_nextActDefId: wf_nextActDefId,
							wf_procDefId: _this.pListData.procDefId,
							wf_procDefKey: _this.procDefKey,
							wf_sendUserId: _this.userInfo.userName,
							wf_sendUserOrgId: _this.userInfo.deptId,
							wf_receivers: wf_receivers,
							wf_curActDefName: _this.pListData.actDefName,
							wf_curActDefId: _this.pListData.actDefId,
							wf_nextActDefName: wf_nextActDefName,
						},
						order: _this.pListData.actDefOrder,
					    review: _this.attributeModel('shenhe') || _this.attributeModel('pizhun'),
					    applyStatus: _this.formSubmit.pass,
					};
				}
				if (_this.attributeModel('fabu') && wf_nextActDefId === 'end') {
					//办结
					formData.recordStatus = 'done'
				} else {
					//进行中
					formData.recordStatus = 'doing'
				}
				formData.editStatus = _this.editStatus
				return addReissueApply(formData).then((res) => {
					if (res.code === 200) {
						this.$refs.uToast.show({
							type: 'success',
							icon: false,
							message: this.$t(`doc.this_dept_process_sub_succ`),
							duration: 2000, //显示时间, 毫秒。设为 0 则不会自动关闭，建议1200
							complete: () => {
								_this.close();
							}
						})
					}
				});
			},
			getSettingDocClassTreeseList() {
				settingDocClassList({
					classStatus: "1",
					neClassType: 'foreign'
				}).then(res => {
					this.docClassList = JSON.parse(JSON.stringify(res.rows))
					this.docClassTree = this.handleTree(res.rows, "id", "parentClassId")
				});
			},
			handleSelectFile() {
				this.drawerShow = true
				this.$nextTick(() => {
					this.$refs.distributeList.init(this.formData.versionId, this.formData.itemList)
				})
			},
			handleCloseChange() {
				this.drawerShow = false
			},
			handleDistributeList(dataList) {
				this.handleCloseChange()
				this.formData.itemList.push(...dataList)
			},
			handleDelete(dataList, index) {
				dataList.splice(index, 1)
			},
			formatterDocClass(row, column, cellValue, index) {
				let _this = this
				if (_this.docClassList) {
					let item = _this.docClassList.find(item => item.id === cellValue)
					return item ? item.className : cellValue
				}
				return cellValue
			},
			openMonitor() {
				uni.$u.route('/pages/workflowList/monitor?procInstId=' + this.procInstId)
			},
			close() {
				uni.switchTab({
					url: '/pages/index'
				});
			},
		},
	};
</script>
<style scoped>
	.document_change_add {
		.fujian .el-textarea__inner {
			border: 0 solid #dcdfe6;
			padding: 0;
		}
	}
</style>
