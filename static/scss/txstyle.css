/*body*/
.uni-body {
	background: #f9fafe !important;
	font-size: 14px;
}

body {
	background: #f9fafe !important;
}


/*uni-page-head 顶部导航*/
.uni-page-head {
	background: #f9fafe !important;
	color: #333 !important;
}

.uni-page-head .uni-btn-icon {
	color: #333 !important;
}

.uni-page-head .uni-page-head__title {
	font-weight: normal !important;
}

/*u-page 内容区域*/
body .u-navbar+.u-page {
	padding-top: 59px;
}

body .u-page {
	padding: 48px 15px 80px 15px;
}

/*foot-btn 底部按钮*/
.foot-btn {
	display: block;
	background-color: #fff;
	padding: 7.5px;
	position: fixed;
	width: 100%;
	bottom: 0;
	left: 0;
	box-sizing: border-box;
	z-index: 9;
	box-shadow: inset 0px 0.5px 0px #e7e7e7;
}

.foot-btn .u-col {
	padding: 7.5px !important;
	box-sizing: border-box;
}

/*white-card 白色卡片*/
.white-card {
	background: #fff;
	margin: 0 0 15px 0;
	border-radius: 4px;
	box-shadow: 0px 2px 8px rgb(75 89 124 / 5%);
}

.white-card .u-form-item .u-form-item__body {
	padding: 10px 15px;
}

.white-card>.u-form-item:last-child .u-line {
	display: none;
}

.white-card .u-steps {
	padding: 15px 0;
}

/*u-button 按钮*/
.u-button.u-button--primary {
	background-color: #013288 !important;
	border-color: #013288 !important;
}

.u-button.u-button--plain.u-button--primary {
	color: #013288 !important;
	background-color: #fff !important;
	border-color: #013288 !important;
}

/*button1*/
.u-button.button1,
.u-button.button1:hover,
.u-button.button1:focus,
.u-button.button1:active {
	background: transparent !important;
	border-color: transparent !important;
	color: #303133 !important;
	padding: 0 !important;
}

.u-button.button1 .u-icon.u-icon--right {
	padding: 0 0 0 5px;
}

/*u-form-item 表单*/
.u-form-item {
	font-size: 14px;
}

.u-form-item .u-line {
	border-color: #f0f0f0 !important;
}

.u-form-item .u-form-item__body .u-form-item__body__left__content__label {
	font-size: 14px;
}

.u-form-item .u-form-item__body .u-input__content__field-wrapper__field {
	font-size: 14px;
}

.u-form-item .u-form-item__body .u-form-item__body__right .u-input__content__field-wrapper__field {
	height: 19px !important;
}

.uni-body .uni-input-input {
	font-size: 14px;
}

.u-form-item .u-form-item__body .u-form-item__body__right .u-button {
	display: inline-flex;
	width: auto;
	margin: 0;
}

.u-form-item .u-form-item__body .item__body__right__content__icon .u-icon__icon {
	color: #999 !important;
}

.u-form-item .u-form-item__body .red .u-icon__icon {
	color: #f56c6c !important;
}

.u-form-item.label-top .u-form-item__body {
	flex-direction: column !important;
}

.u-form-item.label-top .u-form-item__body .u-form-item__body__left {
	margin-bottom: 8px !important;
}

.u-form-item.item-right-col .u-form-item__body .u-form-item__body__right__content .u-form-item__body__right__content__slot {
	flex-direction: column;
}

.u-form-item .u-form-item__body .u-form-item__body__right .u-form-item__body__right__content .u-form-item__body__right__content__slot {
	justify-content: flex-start;
	color: #999;
	font-size: 14px;
}

.u-form-item.item-right-col .u-form-item__body .u-form-item__body__right .u-form-item__body__right__content .u-form-item__body__right__content__slot .u-text+.u-text {
	margin-top: 10px !important;
}

.u-form-item .u-form-item__body .u-form-item__body__right .u-form-item__body__right__content .u-form-item__body__right__content__slot .u-text .u-link {
	justify-content: flex-start;
	text-align: left;
	font-size: 14px;
}

.u-form-item .u-form-item__body .u-form-item__body__right .u-form-item__body__right__content .uni-input-placeholder {
	color: #999 !important;
	text-align: left;
	font-size: 14px;
}

.u-form-item .u-form-item__body .u-form-item__body__right .u-form-item__body__right__content .uni-input-input {
	color: #999 !important;
	text-align: left;
	font-size: 14px;
}

.login-page .u-form-item .u-form-item__body .u-form-item__body__right .u-form-item__body__right__content .uni-input-input,
.login-page .u-form-item .u-form-item__body .u-form-item__body__right .u-form-item__body__right__content .uni-input-placeholder {
	text-align: left;
}

.u-form-item .u-form-item__body .uni-textarea-placeholder {
	color: #999 !important;
	font-size: 14px;
}

.u-form-item .u-form-item__body .uni-textarea-textarea {
	color: #999 !important;
	font-size: 14px;
}

/*粘性布局*/
.sticky-1 {
	top: 0 !important;
}

.sticky-1+.sticky-2 {
	top: 44px !important;
	padding-top: 15px;
}

.sticky-2 {
	padding: 10px 15px;
	background: #f9fafe !important;
}

.sticky-2.top-0 {
	top: 0 !important;
}

.sticky-2 .u-search__content,
.sticky-2 .u-search__content__input {
	background: #fff !important;
}

/*sx-button 筛选按钮*/
.u-search+.sx-button {
	padding-top: 5px;
}

.sx-button {
	position: relative;
	padding: 0 68px 0 0;
}

.sx-button .more {
	width: auto !important;
	line-height: 44px;
	height: 44px !important;
	position: absolute !important;
	color: #333 !important;
	right: 0px;
	top: 5px;
	background: transparent !important;
	border: 0 !important;
	border-radius: 0 !important;
	font-size: 14px !important;
}

.u-search+.sx-button .u-page__tag-item {
	margin-top: 10px;
	margin-bottom: 0;
}

.sx-button .u-page__tag-item {
	display: inline-block;
	margin: 2px 10px 2px 0;
}

.sx-button .u-page__tag-item .u-tag--square {
	border-radius: 24px;
}

.sx-button .u-page__tag-item .u-tag--primary {
	background: rgba(1, 50, 136, 0.05);
	color: #013288;
	border-color: #013288;
}

.sx-button .u-page__tag-item .u-tag--primary .u-tag__text--primary {
	color: #013288;
}

.sx-button .u-page__tag-item .u-tag--primary--plain {
	color: #323233;
	background-color: #fff;
	border-color: #fff;
}

.sx-button .u-page__tag-item .u-tag--primary--plain .u-tag__text--primary--plain {
	color: #323233;
}

/*title-1 标题*/
.title-1 {
	padding: 0 15px;
	line-height: 24px;
	color: #333;
	font-size: 14px;
}

/*popup-card1 弹出层分组*/
.popup-card1 {
	padding: 10px 0 0 0;
}

/*tag-group 标签组*/
.tag-group {
	padding: 7.5px;
}

.tag-group::before,
.tag-group::after {
	content: "";
	display: table;
}

.tag-group::after {
	clear: both;
}

.tag-group .u-transition {
	width: calc(33.333333333% - 15px);
	margin: 0 7.5px 10px 7.5px;
	float: left;
}

.tag-group .u-transition .u-tag {
	position: relative;
}

.tag-group .u-transition .u-tag .u-tag__icon {
	margin: 0 !important;
	position: absolute;
	right: 2px;
	top: 0;
}

.tag-group .u-transition .u-tag .u-tag__icon .u-icon__icon {
	color: transparent !important;
}

.tag-group .u-transition.active .u-tag .u-tag__icon .u-icon__icon {
	color: #013288 !important;
}

.tag-group .u-transition .u-tag--primary {
	background: #F0F5FA;
	border-color: #F0F5FA;
	color: #a5b1be;
	padding: 5px 5px;
}

.tag-group .u-transition .u-tag--primary .u-tag__text {
	color: #333;
	display: block;
	width: 100%;
	text-align: center;
}

.tag-group .u-transition.active .u-tag--primary {
	background: #fff;
	border-color: #013288;
	color: #013288;
}

.tag-group .u-transition.active .u-tag--primary .u-tag__text {
	color: #013288;
}

/*tabs-1 标签*/
.tabs-1 {
	background: #f9fafe;
	align-items: center !important;
	align-content: center !important;
}

.tabs-1 .u-tabs__wrapper,
.tabs-1 .u-tabs__wrapper__scroll-view-wrapper,
.tabs-1 .u-tabs__wrapper__scroll-view,
.tabs-1 .u-tabs__wrapper__nav {
	align-items: center !important;
	align-content: center !important;
}

.tabs-1 .u-tabs__wrapper__nav__item {
	padding: 0 16px !important;
}

.tabs-1 .u-tabs__wrapper__nav__item .u-tabs__wrapper__nav__item__text {
	font-size: 16px !important;
}

/*tabs-2 标签*/
.tabs-2 .u-tabs__wrapper__nav__item .u-tabs__wrapper__nav__item__text {
	font-size: 14px !important;
}

/*file-list 文件列表*/
.file-list {
	padding: 0 0 70px 0;
}

.file-list .list {
	display: block;
	width: calc(100% - 30px);
	background: #FFFFFF;
	padding: 15px 15px;
	box-sizing: border-box;
	margin: 0 15px 15px 15px;
	border-radius: 4px;
	box-shadow: 0px 2px 8px rgb(75 89 124 / 5%);
}

.file-list .list .list-table {
	display: table;
	width: 100%;
}

.file-list .list .list-table .list-ico {
	display: table-cell;
	vertical-align: middle;
	width: 65px;
}

.file-list .list .list-table .list-ico uni-image {
	width: 50px;
	height: 50px;
}

.file-list .list .list-table .list-text {
	display: table-cell;
	vertical-align: top;
	position: relative;
}

.file-list .list .list-table .list-text .title {}

.file-list .list .list-table .list-text .tag-group {
	padding: 0;
}

.file-list .list .list-table .list-text .tag-group .u-transition {
	float: left;
	width: auto;
	display: inline-flex;
	margin: 8px 8px 0 0;
}

.file-list .list .list-table .list-text .tag-group .u-transition .u-tag--primary {
	padding: 0 5px;
}

.file-list .list .list-table .list-text .tag-group .u-transition .u-tag--primary .u-tag__text {
	color: #A5B2BE;
}

.file-list .list .list-table .list-text .more {
	position: absolute;
	display: inline-block;
	width: auto;
	border: 0;
	top: 0;
	right: 0;
	height: 20px;
	padding: 0;
}

.file-list .list .list-table .list-text .more .u-icon {
	transform: rotate(90deg);
	-ms-transform: rotate(90deg);
	/* IE 9 */
	-moz-transform: rotate(90deg);
	/* Firefox */
	-webkit-transform: rotate(90deg);
	/* Safari 和 Chrome */
	-o-transform: rotate(90deg);
	/* Opera */
}

.file-list .list .list-table .list-text .more .u-icon__icon {
	color: #999 !important;
}

.file-list .list .list-lab {
	display: block;
	margin: 10px 0 0 0;
	padding: 0;
}

.file-list .list .list-lab .lab {
	display: inline-block;
	color: #999;
	font-size: 13px;
	position: relative;
	padding: 0 15px;
}

.file-list .list .list-lab>.lab:first-child {
	padding-left: 0;
}

.file-list .list .list-lab .lab::before {
	content: "";
	position: absolute;
	right: 0;
	top: calc(50% - 5px);
	width: 1px;
	height: 10px;
	background: #ccc;
}

.file-list .list .list-lab>.lab:last-child::before {
	content: none;
}

.file-list .list .list-lab>.lab uni-image {
	float: left;
	width: 15px;
	height: 15px;
	margin: 1px 5px 0 0;
}

.file-list .list .list-lab>.lab uni-view {
	float: left;
	margin: 1px 5px 0 0;
}

.file-list .list .list-lab>.lab .u-icon__icon {
	color: #999999 !important;
	font-size: 15px !important;
}

/*todo-list 待办列表*/
.todo-list {
	background-color: #F8F8F8;
	padding: 0 0 70px 0;
}

.todo-list>.list:last-child {
	border: 0;
}

.todo-list .list {
	display: block;
	background: #FFFFFF;
	padding: 15px 15px;
	box-sizing: border-box;
	border-bottom: 1px solid #eee;
}

.todo-list .list .title-time {
	display: table;
	width: 100%;
	margin: 0;
	padding: 0;
}

.todo-list .list .title-time .title {
	display: table-cell;
	vertical-align: top;
	color: #333;
	font-size: 14px;
}

.todo-list .list .title-time .time {
	display: table-cell;
	vertical-align: top;
	text-align: right;
	color: #8C8C8C;
	font-size: 14px;
}

.todo-list .list .text {
	display: block;
	margin: 5px 0 0 0;
	padding: 0;
	color: #8C8C8C;
	font-size: 13px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

/*message-list 消息列表*/
.message-list {
	padding: 5px 15px 15px 15px;
}

.message-list .list {
	display: block;
	padding: 15px;
	background-color: #FFFFFF;
	border-radius: 4px;
	box-shadow: 0px 2px 8px rgb(75 89 124 / 5%);
}

.message-list .list+.list {
	margin-top: 15px;
}

.message-list .list .title {
	display: block;
	color: #333333;
	font-size: 14px;
	border-bottom: 1px solid #f0f0f0;
	padding: 0 0 10px 0;
}

.message-list .list .title .u-badge {
	float: left;
	margin: 6px 5px 0 0;
}

.message-list .list .text {
	display: block;
	min-height: 38px;
	color: #999;
	font-size: 13px;
	margin: 10px 0 0 0;
	line-height: 1.5;
}

.message-list .list .time {
	display: block;
	color: #ccc;
	font-size: 12px;
	margin: 10px 0 0 0;
}

/*颜色*/
.blue {
	color: #3c9cff;
}

.green {
	color: #5ac725;
}

.orange {
	color: #f9ae3d;
}

.red {
	color: #f56c6c;
}

.gray {
	color: #999;
}

/*u-border*/
body .u-border {
	border-color: #f0f0f0 !important;
}

/*me-page 个人中心*/
.me-page {
	padding: 15px;
}

.user-card {
	display: block;
	margin: 20px 0 30px 0;
}

.user-card .user-img {
	width: 100px;
	height: 100px;
	margin: 0 auto 20px auto;
	display: block;
}

.user-card .name {
	display: block;
	text-align: center;
}

.user-card .name .big {
	font-size: 20px;
	font-weight: 600;
	padding: 3px 10px;
	color: #333;
}

.user-card .name .small {
	font-size: 16px;
	font-weight: normal;
	padding: 3px 10px;
	color: #666;
}

.user-card .text {
	text-align: center;
	margin: 20px 0;
}

.user-card .text .txt {
	display: inline-block;
	background-color: #E9ECFF;
	color: #333;
	font-size: 14px;
	padding: 5px 8px;
}

.user-card .text .txt.active {
	background-color: #013288;
	color: #fff;
}

.user-card .text>.txt:first-child {
	border-radius: 4px 0 0 4px;
}

.user-card .text>.txt:last-child {
	border-radius: 0 4px 4px 0;
}

/*card1 详情*/
.white-card .card1 {
	margin: 0 15px;
	padding: 15px 0;
}

.white-card .card1+.card1 {
	margin-top: 0px;
}

.card1 {
	display: block;
}

.card1+.card1 {
	border-top: 1px solid #f0f0f0;
	padding-top: 15px;
	margin-top: 15px;
}

.card1 .card-title {
	display: block;
	margin: 0 0 10px 0;
	padding: 0;
	color: #000;
	font-size: 14px;
	font-weight: bold;
}

.card1 .text-list {
	display: block;
	margin: 0;
	padding: 0;
	font-size: 14px;
}

.card1 .text-list+.text-list {
	margin-top: 10px;
}

.card1 .text-list .clum {
	display: inline;
	color: #999999;
}

.card1 .text-list .text {
	display: inline;
	color: #333;
}

.card1 .text-list .text .u-button {
	width: auto;
	margin: 0 10px 0 0;
	display: inline-block;
	height: 34px;
	line-height: 34px;
	padding: 0 10px;
}

/* .card1 .text-list .text img{
	display: inline-block;
	width: auto;
	max-width: 100%;
	margin: 0 10px 5px 0;
} */
.card1 .text-list.block-list {}

.card1 .text-list.block-list .clum {
	display: block;
}

.card1 .text-list.block-list .text {
	display: block;
	margin: 5px 0 0 0;
}

.card1 .text-list.table-list {
	display: table;
	width: 100%;
}

.card1 .text-list.table-list .clum {
	display: table-cell;
	vertical-align: top;
	width: 85px;
}

.card1 .text-list.table-list .text {
	display: table-cell;
	vertical-align: top;
}

.card1 .text-list.table-list .text .u-text+.u-text {
	margin-top: 4px !important;
}

.u-text .u-link {
	font-size: 14px !important;
	color: #225FC7 !important;
}

/*u-collapse 展开折叠组件*/
.u-collapse {}

.u-collapse .u-collapse-item {
	background: #fff;
	margin: 0 0 15px 0;
	box-shadow: 0px 2px 8px rgb(75 89 124 / 5%);
	border-radius: 4px;
}

.u-collapse .u-line {
	display: none;
}

.u-collapse .u-collapse-item .u-collapse-item__content+.u-line {
	display: none;
}

.u-collapse .u-collapse-item .u-cell__title-text {
	color: #333;
	font-size: 14px;
	font-weight: bold;
	position: relative;
	padding: 0 0 0 8px;
}

.u-collapse .u-collapse-item .u-cell__title-text::before {
	display: block;
	content: "";
	position: absolute;
	left: 0;
	top: calc(50% - 6px);
	width: 3px;
	height: 12px;
	background: #013288;
}

/*vi-table 表格*/
.vi-table {
	width: 100%;
	border-top: 1px solid #f0f0f0;
	border-left: 1px solid #f0f0f0;
	font-size: 14px;
}

.vi-table td,
.vi-table th {
	border-right: 1px solid #f0f0f0;
	border-bottom: 1px solid #f0f0f0;
	text-align: left;
	vertical-align: top;
	padding: 8px 5px;
}

.vi-table td {
	color: #333;
}

.vi-table th {
	color: #333;
	font-weight: normal;
}

/*line-list 流程记录*/
.line-list {
	display: block;
}

.line-list .list {
	display: block;
	position: relative;
	padding: 0 0 20px 30px;
}

.line-list>.list:last-child {
	padding-bottom: 10px;
}

.line-list .list::before {
	position: absolute;
	left: 10px;
	top: 3px;
	content: "";
	display: block;
	width: 1px;
	height: 100%;
	border-left: 1px solid #eee;
}

.line-list>.list:last-child::before {
	content: none;
}

.line-list .list .point {
	position: absolute;
	left: 0;
	top: 0;
	display: inline-block;
	width: 20px;
	height: 20px;
	line-height: 20px;
	border-radius: 100%;
	text-align: center;
	border: 1px solid #013288;
	box-sizing: border-box;
	z-index: 1;
	background: #fff;
}

.line-list .list.success .point {
	border-color: #53c21d;
}

.line-list .list.lose .point {
	border-color: #e45656;
}

.line-list .list .point::before {
	content: "";
	position: absolute;
	left: 3px;
	top: 3px;
	display: block;
	width: 12px;
	height: 12px;
	background-color: #013288;
	border-radius: 100%;
}

.line-list .list.success .point::before,
.line-list .list.lose .point::before {
	content: none;
}

.line-list .list .point .u-icon .u-icon__icon {
	line-height: 20px !important;
	text-align: center;
	padding: 0 0 0 2px;
	font-size: 14px !important;
	color: transparent !important;
}

.line-list .list.success .point .u-icon .u-icon__icon {
	color: #53c21d !important;
}

.line-list .list.lose .point .u-icon .u-icon__icon {
	color: #e45656 !important;
}

.line-list .list .title-time {
	display: table;
	width: 100%;
}

.line-list .list .title-time .title {
	display: table-cell;
	vertical-align: top;
	color: #333;
	font-size: 14px;
}

.line-list .list .title-time .time {
	display: table-cell;
	vertical-align: top;
	text-align: right;
	color: #ccc;
	font-size: 12px;
}

.line-list .list .text {
	display: block;
	margin: 15px 0 0 0;
}

.line-list .list .text .txt {
	display: block;
	color: #666;
	font-size: 13px;
}

.line-list .list .text .lab {
	display: inline-block;
	background: #f0f0f0;
	color: #999;
	font-size: 12px;
	padding: 3px 6px;
	border-radius: 2px;
	margin: 5px 10px 0 0;
}

/*u-radio*/
.u-radio .u-radio__text {
	font-size: 14px !important;
}

/*u-checkbox*/
.u-checkbox .u-checkbox__text {
	font-size: 14px !important;
}

/*login-page 登录页*/
.login-page {
	display: block;
	background: #fff url('@/static/images/bgimg-p.png') no-repeat top center;
	background-size: cover;
	position: fixed;
	width: 100%;
	height: 100%;
	z-index: 998;
	top: 0;
}

.login-page .logo {
	display: block;
	width: 48px;
	height: 54px;
	margin: 90px 0 20px 40px;
}

.login-page .title {
	display: block;
	color: #000;
	font-size: 20px;
	margin: 0 0 70px 40px;
}

.login-page .u-form {
	padding: 0 40px;
}

.login-page .u-form .u-form-item {
	margin: 24px 0;
	border-bottom: 1px solid #eee;
}

.login-page .u-form .u-form-item .u-form-item__body__left {
	width: 30px !important;
}

.login-page .u-form .u-form-item .u-icon__icon {
	font-size: 24px !important;
	color: #999 !important;
}

.login-page .u-form .u-form-item .u-input__content__field-wrapper__field {
	color: #333 !important;
	font-size: 16px !important;
}

.login-page .u-button {
	margin: 50px 40px 0px 40px;
	width: calc(100% - 80px) !important;
	background: #03388C !important;
	color: #fff !important;
	border: 0 !important;
	border-radius: 44px !important;
	height: 44px !important;
	line-height: 44px;
	letter-spacing: 4px;
	font-size: 16px !important;
}

/*index-page 首页*/
.index-page {
	padding: 15px 15px 70px 15px;
}

/*menu-list 首页快捷入口*/
.menu-list {
	display: flex;
	padding: 20px 0;
}

.menu-list .list {
	flex: 1;
	text-align: center;
	position: relative;
}

.menu-list .list .tag {
	position: absolute;
	z-index: 2;
	left: 50%;
}

.menu-list .list uni-image {
	width: 50px;
	height: 50px;
}

.menu-list .list .title {
	display: block;
	text-align: center;
	color: #333;
	font-size: 14px;
}

/*notice 文件速递*/
.index-page .notice {
	margin-left: 0;
	margin-right: 0;
}

.notice {
	margin: 15px;
	border-radius: 4px;
	background-color: #fff;
	padding: 10px;
	display: block;
}

.notice uni-image {
	float: left;
	width: 19px;
	height: 19px;
	margin: 0 5px 0 0;
}

.notice .title {
	float: left;
	color: #333333;
	font-size: 14px;
	position: relative;
	padding: 0 10px 0 0;
	margin: 0 10px 0 0;
}

.notice .title::after {
	content: "";
	display: block;
	position: absolute;
	right: 0;
	top: calc(50% - 5px);
	width: 1px;
	height: 10px;
	background-color: #999;
}

.notice .lab {
	float: left;
	padding: 0 5px;
	margin: 0 5px 0 0;
	height: 19px;
	line-height: 19px;
	font-size: 12px;
}

.notice .lab.lab-add {
	background: #e2f2ff;
	color: #007aff;
}

.notice .lab.lab-invalid {
	background: #fff0f0;
	color: #ff5230;
}

.notice .lab.lab-revision {
	background: #fff4e3;
	color: #ff9922;
}

.notice .lab.lab-borrow {
	background: #f1eeff;
	color: #856fff;
}

.notice .text {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.notice .u-notice-bar {
	background: #fff !important;
	padding: 0 !important;
}

.notice .u-notice-bar .u-icon__icon {
	color: #FC7032 !important;
}

.notice .u-notice-bar .uicon-close {
	display: none;
}

.notice .u-notice-bar .u-notice__swiper__item__text {
	color: #333 !important;
}

/*tag 徽标*/
.tag {
	display: inline-block;
	background: #E34D59;
	color: #fff;
	font-size: 12px;
	font-weight: normal;
	min-width: 16px;
	text-align: center;
	height: 16px;
	border-radius: 16px;
	padding: 0 2px;
	box-sizing: border-box;
	margin: 0 5px;
}

/*index-card 首页卡片*/
.index-page .index-card {
	margin-left: 0;
	margin-right: 0;
}

.index-card {
	margin: 15px;
	border-radius: 3px;
	box-shadow: 0px 2px 8px rgb(75 89 124 / 5%);
}

/*card-head 卡片标题栏*/
.card-head {
	display: flex;
	align-items: center;
	background: #fff;
	height: 44px;
	margin: 0;
	padding: 0 15px;
	box-sizing: border-box;
	border-radius: 4px 4px 0 0;
}

.card-head .head-title {
	display: block;
	flex: 1;
	font-size: 14px;
	color: #333;
	font-weight: bold;
	position: relative;
	padding: 0 0 0 8px;
	;
}

.card-head .head-title::before {
	display: block;
	content: "";
	position: absolute;
	left: 0;
	top: calc(50% - 6px);
	width: 3px;
	height: 12px;
	background: #013288;
}

.card-head .head-btn {
	display: block;
}

.card-head .head-btn .u-button {
	display: block;
	height: 28px;
	line-height: 26px;
	padding: 0 10px;
	width: auto;
	margin: 0 0 0 10px;
}

.index-card .card-head .head-btn .more {}

.index-card .card-head .head-btn .more .u-icon {
	display: inline-block;
}

/*index-list 首页*/
.index-list {
	display: block;
}

.index-list .list {
	display: table;
	width: 100%;
	background-color: #fff;
	padding: 15px 0;
	table-layout: fixed;
}

.index-list>.list:last-child {
	border-radius: 0 0 4px 4px;
}

.index-list .list .list-img {
	display: table-cell;
	vertical-align: middle;
	text-align: left;
	width: 70px;
	padding: 0 0 0 15px;
	box-sizing: border-box;
}

.index-list .list .list-img uni-image {
	width: 45px;
	height: 45px;
}

.index-list .list .list-text {
	display: table-cell;
	vertical-align: top;
	padding: 0 15px 0 0;
}

.index-list .list .list-text .title-time {
	display: table;
	width: 100%;
	line-height: 19px;
}

.index-list .list .list-text .title-time .title {
	display: table-cell;
	vertical-align: top;
	color: #333;
	font-size: 14px;
}

.index-list .list .list-text .title-time .time {
	display: table-cell;
	vertical-align: top;
	color: #aaa;
	font-size: 12px;
	text-align: right;
}

.index-list .list .list-text .title {
	display: block;
	color: #333;
	font-size: 14px;
}

.index-list .list .list-text .text {
	display: block;
	color: #999;
	font-size: 14px;
	margin: 8px 0 0 0;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.index-list .list .list-text .tag-group {
	padding: 0;
}

.index-list .list .list-text .tag-group .u-transition {
	float: inherit;
	width: auto;
	display: inline-flex;
	margin: 8px 8px 0 0;
}

.index-list .list .list-text .tag-group .u-transition .u-tag--primary {
	padding: 0 5px;
}

.index-list .list .list-text .tag-group .u-transition .u-tag--primary .u-tag__text {
	color: #A5B2BE;
}

/*u-tabbar 底部菜单*/
.u-tabbar .u-tabbar__content {
	border: 0 !important;
	box-shadow: inset 0px 0.5px 0px #e7e7e7;
}

.u-tabbar-item__icon uni-image {
	width: 20px;
	height: 20px;
}

.uni-tabbar-bottom .uni-tabbar,
.uni-tabbar {
	background: #fff !important;
	box-shadow: inset 0px 0.5px 0px #e7e7e7;
}

.uni-tabbar .uni-tabbar-border {
	display: none;
}

.uni-tabbar .uni-tabbar__item .uni-tabbar__bd .uni-tabbar__icon {
	width: 20px !important;
	height: 20px !important;
}

/*u-cell-group 单元格*/
.me-page .u-cell-group,
.u-page .u-cell-group {
	margin-left: 0;
	margin-right: 0;
}

.u-cell-group {
	background: #fff;
	box-shadow: 0px 2px 8px rgb(75 89 124 / 5%);
	margin: 15px;
	border-radius: 4px;
}

.u-cell-group .u-cell-group__wrapper {
	padding: 0 15px;
}

.u-cell-group .u-cell {}

.u-cell-group .u-cell uni-image {
	width: 16px;
	height: 16px;
}

.u-cell-group .u-cell .u-cell__body {
	padding: 12px 0;
}

.u-cell-group .u-cell .u-cell__body .u-cell__title-text {
	font-size: 14px !important;
	color: #333;
	line-height: 18px;
}

.u-cell-group .u-cell+.u-cell {
	border-top: 1px solid #f0f0f0;
}

.u-cell-group .u-line {
	display: none;
}

.u-cell .u-icon--right .u-icon__icon {
	color: #999 !important;
}

/*u-form 表单*/
.white-card .u-form-item {
	margin: 0 15px;
}

.white-card .u-form-item .u-form-item__body {
	padding: 12px 0;
}

/*u-empty 暂无*/
.index-card .u-empty {
	background: #fff;
	padding: 0 0 25px 0;
}

/*ft-card 表单嵌套表格*/
.card-head+.ft-card {
	padding-top: 0;
}

.ft-card {
	display: block;
	padding: 10px 15px 0 15px;
}

.ft-card+.ft-card {
	margin-top: 10px;
}

.ft-card .chead {
	display: flex;
	align-items: center;
	padding: 0;
}

.ft-card .chead .htitle {
	display: block;
	flex: 1;
	font-size: 14px;
	color: #333;
	font-weight: bold;
}

.ft-card .chead .hbtn {
	display: block;
}

.ft-card .chead .hbtn .u-button {
	height: 28px;
}

.ft-card .ctr {
	display: block;
	padding: 10px 0;
}

.ft-card>.ctr:first-child {
	padding-top: 0;
}

.ft-card>.ctr:last-child {
	padding-bottom: 0;
}

.ft-card .ctr+.ctr {
	border-top: 1px solid #f0f0f0;
}

.ft-card .ctr .clist {
	display: flex;
	align-items: center;
	line-height: 19px;
	font-size: 14px;
	padding: 10px 0;
}

.ft-card .ctr .clist .clum {
	display: block;
	width: 100px;
	color: #999;
}

.ft-card .ctr .clist .text {
	display: block;
	flex: 1;
	color: #333;
}

.ft-card .ctr .clist .u-button {
	width: auto;
	display: inline-flex;
	height: 26px;
	padding: 0 8px;
	border-radius: 2px;
}