import request from '@/utils/request'

// 查询文件分发记录列表
export function listDistributeLog(query) {
  return request({
    url: '/process/distributeLog/list',
    method: 'get',
    params: query
  })
}
// 查询文件分发记录列表
export function listDistributeLoglistForPrint(query) {
  return request({
    url: '/process/distributeLog/listForPrint',
    method: 'get',
    params: query
  })
}
// 查询文件分发记录列表
export function printLoglist(query) {
  return request({
    url: '/process/printLog/list',
    method: 'get',
    params: query
  })
}

// 查询文件分发记录列表
export function listDistributeLoglistForSign(query) {
  return request({
    url: '/process/distributeLog/listForSign',
    method: 'get',
    params: query
  })
}

// 查询文件分发记录列表
export function listNoProcessDistributeLog(query) {
  return request({
    url: '/process/distributeLog/listNoProcess',
    method: 'get',
    params: query
  })
}

// 查询文件分发记录详细
export function getDistributeLog(id) {
  return request({
    url: '/process/distributeLog/appid/' + id,
    method: 'get'
  })
}

// 新增文件分发记录
export function addDistributeLog(data) {
  return request({
    url: '/process/distributeLog',
    method: 'post',
    data: data
  })
}

// 修改文件分发记录
export function updateDistributeLog(data) {
  return request({
    url: '/process/distributeLog',
    method: 'put',
    data: data
  })
}

// 删除文件分发记录
export function delDistributeLog(id) {
  return request({
    url: '/process/distributeLog/' + id,
    method: 'delete'
  })
}

// 签收文件
export function signFile(id) {
  return request({
    url: '/process/distributeLog/sign/' + id,
    method: 'get'
  })
}
