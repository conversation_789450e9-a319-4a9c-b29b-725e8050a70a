import request from '@/utils/request'

// 查询文件回收记录列表
export function listRecoveryLog(query) {
  return request({
    url: '/process/recoveryLog/list',
    method: 'get',
    params: query
  })
}

// 查询文件回收记录详细
export function getRecoveryLog(id) {
  return request({
    url: '/process/recoveryLog/' + id,
    method: 'get'
  })
}

// 新增文件回收记录
export function addRecoveryLog(data) {
  return request({
    url: '/process/recoveryLog',
    method: 'post',
    data: data
  })
}

// 修改文件回收记录
export function updateRecoveryLog(data) {
  return request({
    url: '/process/recoveryLog',
    method: 'put',
    data: data
  })
}

// 删除文件回收记录
export function delRecoveryLog(id) {
  return request({
    url: '/process/recoveryLog/' + id,
    method: 'delete'
  })
}
