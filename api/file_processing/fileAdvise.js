import request from '@/utils/request'

// 查询文件建议列表
export function listFileAdvise(query) {
  return request({
    url: '/process/fileAdvise/list',
    method: 'get',
    params: query
  })
}

// 查询文件建议详细
export function getFileAdvise(id) {
  return request({
    url: '/process/fileAdvise/' + id,
    method: 'get'
  })
}

// 新增文件建议
export function addFileAdvise(data) {
  return request({
    url: '/process/fileAdvise',
    method: 'post',
    data: data
  })
}

// 修改文件建议
export function updateFileAdvise(data) {
  return request({
    url: '/process/fileAdvise',
    method: 'put',
    data: data
  })
}

// 删除文件建议
export function delFileAdvise(id) {
  return request({
    url: '/process/fileAdvise/' + id,
    method: 'delete'
  })
}
