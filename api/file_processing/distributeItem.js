import request from '@/utils/request'

// 查询文件分发记录条目列表
export function listDistributeItem(query) {
  return request({
    url: '/process/distributeItem/list',
    method: 'get',
    params: query
  })
}

// 查询文件分发记录条目列表
export function listDistributeItemByAppid(query) {
  return request({
    url: '/process/distributeItem/listByAppid',
    method: 'get',
    params: query
  })
}

// 查询文件分发记录条目详细
export function getDistributeItem(id) {
  return request({
    url: '/process/distributeItem/' + id,
    method: 'get'
  })
}

// 新增文件分发记录条目
export function addDistributeItem(data) {
  return request({
    url: '/process/distributeItem',
    method: 'post',
    data: data
  })
}

// 修改文件分发记录条目
export function updateDistributeItem(data) {
  return request({
    url: '/process/distributeItem',
    method: 'put',
    data: data
  })
}

// 删除文件分发记录条目
export function delDistributeItem(id) {
  return request({
    url: '/process/distributeItem/' + id,
    method: 'delete'
  })
}

export function updateDistributeById(data) {
  return request({
    url: '/process/recoveryLogItem/recovery',
    method: 'post',
    data: data
  })
}
export function printLogItemprint(data) {
  return request({
    url: '/process/printLogItem/print',
    method: 'post',
    data: data
  })
}
export function printLogItemreprint(data) {
  return request({
    url: '/process/printLogItem/reprint',
    method: 'post',
    data: data
  })
}
