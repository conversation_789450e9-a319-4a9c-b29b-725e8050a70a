import request from '@/utils/request'

// 查询文件变更申请列表
export function listChangeApply(query) {
  return request({
    url: '/process/changeApply/list',
    method: 'get',
    params: query
  })
}

export function getInfoByBpmnId(bpmnId) {
  return request({
    url: '/process/changeApply/bpmnId/'+bpmnId,
    method: 'get',
  })
}

// 查询文件变更申请详细
export function getChangeApply(id) {
  return request({
    url: '/process/changeApply/' + id,
    method: 'get'
  })
}

// 新增文件变更申请
export function addChangeApply(data) {
  return request({
    url: '/process/changeApply',
    method: 'post',
    data: data
  })
}

// 修改文件变更申请
export function updateChangeApply(data) {
  return request({
    url: '/process/changeApply',
    method: 'put',
    data: data
  })
}

// 删除文件变更申请
export function delChangeApply(id) {
  return request({
    url: '/process/changeApply/' + id,
    method: 'delete'
  })
}
