import request from '@/utils/request'

// 查询文件借阅申请列表
export function listBorrowApply(query) {
  return request({
    url: '/process/borrowApply/list',
    method: 'get',
    params: query
  })
}

// 查询文件借阅申请详细
export function getBorrowApply(id) {
  return request({
    url: '/process/borrowApply/' + id,
    method: 'get'
  })
}

// 新增文件借阅申请
export function addBorrowApply(data) {
  return request({
    url: '/process/borrowApply',
    method: 'post',
    data: data
  })
}

// 修改文件借阅申请
export function updateBorrowApply(data) {
  return request({
    url: '/process/borrowApply',
    method: 'put',
    data: data
  })
}

// 删除文件借阅申请
export function delBorrowApply(id) {
  return request({
    url: '/process/borrowApply/' + id,
    method: 'delete'
  })
}
