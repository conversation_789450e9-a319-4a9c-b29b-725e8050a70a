import request from '@/utils/request'

// 查询文件打印记录列表
export function listPrintLog(query) {
  return request({
    url: '/process/printLog/list',
    method: 'get',
    params: query
  })
}

/**
 * 打印记录明细
 * @param query
 * @returns {*}
 */
export function listPrintLogItem(query) {
  return request({
   // url: '/process/distributeItem/list',
    url: '/process/distributeItem/listByAppid',
    method: 'get',
    params: query
  })
}
export function printLogItemlist(query) {
  return request({
   // url: '/process/distributeItem/list',
    url: '/process/printLogItem/list',
    method: 'get',
    params: query
  })
}
export function listPrintLogItemlistByDocId(query) {
  return request({
   // url: '/process/distributeItem/list',
    url: '/process/distributeItem/listByDocId',
    method: 'get',
    params: query
  })
}
export function distributeItemListByAppid(query) {
  return request({
    url: '/process/distributeItem/listByAppid',
    method: 'get',
    params: query
  })
}
export function distributeItemlistByDisId(query) {
  return request({
    url: '/process/distributeItem/listByDisId',
    method: 'get',
    params: query
  })
}

export function listRecoveryByAppid(query) {
  return request({
    url: '/process/recoveryLogItem/list',
    method: 'get',
    params: query
  })
}
//查询文件重打记录列表
export function processReprintLogList(query) {
  return request({
    url: '/process/printLogItemDetail/list',
    method: 'get',
    params: query
  })
}

// 查询文件打印记录详细
export function getPrintLog(id) {
  return request({
    url: '/process/printLog/' + id,
    method: 'get'
  })
}

// 新增文件打印记录
export function addPrintLog(data) {
  return request({
    url: '/process/printLog',
    method: 'post',
    data: data
  })
}
// 新增文件重打记录
export function processReprintLog(data) {
  return request({
    url: '/process/reprintLog',
    method: 'post',
    data: data
  })
}

// 修改文件打印记录
export function updatePrintLog(data) {
  return request({
    url: '/process/printLog',
    method: 'put',
    data: data
  })
}

// 删除文件打印记录
export function delPrintLog(id) {
  return request({
    url: '/process/printLog/' + id,
    method: 'delete'
  })
}
