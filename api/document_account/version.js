import request from '@/utils/request'

// 查询文件版本记录列表
export function listVersion(query) {
  return request({
    url: '/process/version/list',
    method: 'get',
    params: query
  })
}

// 查询文件版本外部门列表
export function listOtherDept(query) {
  return request({
    url: '/process/version/list/other/dept',
    method: 'get',
    params: query
  })
}

export function distributeLogcheckSign(query) {
  return request({
    url: '/process/distributeLog/checkSign',
    method: 'get',
    params: query
  })
}

// 查询文件版本记录详细
export function getVersion(id) {
  return request({
    url: '/process/version/' + id,
    method: 'get'
  })
}

// 新增文件版本记录
export function addVersion(data) {
  return request({
    url: '/process/version',
    method: 'post',
    data: data
  })
}

// 修改文件版本记录
export function updateVersion(data) {
  return request({
    url: '/process/version',
    method: 'put',
    data: data
  })
}

// 删除文件版本记录
export function delVersion(id) {
  return request({
    url: '/process/version/' + id,
    method: 'delete'
  })
}
