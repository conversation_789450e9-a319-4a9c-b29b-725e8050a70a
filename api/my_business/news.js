import request from '@/utils/request'


export function queryNews(data) {
	return request({
		url: '/process/doc-message/query',
		method: 'post',
		data
	})
}

export function readNews(id) {
	return request({
		url: '/process/doc-message/read?id=' + id,
		method: 'get'
	})
}

export function getNewsNum(data) {
	return request({
		url: '/process/doc-message/unread-num',
		method: 'post',
		data: data
	})
}