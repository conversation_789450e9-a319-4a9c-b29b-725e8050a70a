import request from '@/utils/request'

// 查询文件变更操作申请培训记录列表
export function listModifyApplyTrain(query) {
	return request({
		url: '/process/modifyApplyTrain/list',
		method: 'get',
		params: query
	})
}

// 查询文件变更操作申请培训记录详细
export function getModifyApplyTrain(id) {
	return request({
		url: '/process/modifyApplyTrain/' + id,
		method: 'get'
	})
}

// 新增文件变更操作申请培训记录
export function addModifyApplyTrain(data) {
	return request({
		url: '/process/modifyApplyTrain',
		method: 'post',
		data: data
	})
}

// 新增文件变更操作申请培训记录
export function updateModifyApplyTrainList(data) {
	return request({
		url: '/process/modifyApplyTrain/update/list',
		method: 'post',
		data: data,
	})
}

export function getModifyApplyTrainList(versionId) {
	return request({
		url: '/process/modifyApplyTrain/list/' + versionId,
		method: 'post',
	})
}

// 修改文件变更操作申请培训记录
export function updateModifyApplyTrain(data) {
	return request({
		url: '/process/modifyApplyTrain',
		method: 'put',
		data: data
	})
}

// 删除文件变更操作申请培训记录
export function delModifyApplyTrain(id) {
	return request({
		url: '/process/modifyApplyTrain/' + id,
		method: 'delete'
	})
}

export function listExtraApplyItem(query) {
	return request({
		url: '/process/extraApply/item/list',
		method: 'get',
		params: query
	})
}
export function addExtraApplyByBpmnId(bpmnId) {
	return request({
		url: '/process/extraApply/bpmnId/' + bpmnId,
		method: 'get'
	})
}
export function getBorrowApplyByBpmnId(bpmnId) {
	return request({
		url: '/process/borrowApply/bpmnId/' + bpmnId,
		method: 'get'
	})
}
export function listBorrowApplyItem(data) {
	return request({
		url: '/process/borrowApplyItem/list',
		method: 'get',
		data
	})
}
// 查询文件分发明细列表
export function listDistribute(query) {
	return request({
		url: '/process/distribute/list',
		method: 'get',
		params: query
	})
}
export function listPrintGroup(query) {
	return request({
		url: '/process/distribute/list/print/group',
		method: 'get',
		params: query
	})
}
// 查询文件补发申请详情列表
export function listReissueApplyItem(query) {
	return request({
		url: '/process/reissueApplyItem/list',
		method: 'get',
		params: query
	})
}