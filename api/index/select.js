import request from '@/utils/request'

// 文件预览日志
export function indexSelectReadList(query) {
    return request({ 
        url: '/process/index/selectPreviewStatistic',
        method: 'get',
        params: query
    })
}
export function indexSelectFileEffect(data) {
    return request({
        url: '/process/index/selectFileEffect',
        method: 'post',
        data: data
    })
}
export function indexSelectFileStatistic(data) {
    return request({
        url: '/process/index/selectFileStatistic',
        method: 'post',
        data: data
    })
}
export function indexSelectFileAdvise(data) {
    return request({
        url: '/process/index/selectFileAdvise',
        method: 'post',
        data: data
    })
}
// 查询我的消息
export function indexSelectMessage(data) {
    return request({
        url: '/process/doc-message/query',
        method: 'post',
        data: data
    })
}


// 台账总数，按照状态进行统计
export function queryDocAccount(query) {
    return request({ 
        url: '/process/index/queryDocAccount',
        method: 'get',
        params: query
    })
}

// 阅知人次
export function queryViewCount(query) {
    return request({ 
        url: '/process/index/queryViewCount',
        method: 'get',
        params: query
    })
}

// 流程总数
export function queryFlowCount(query) {
    return request({ 
        url: '/process/index/queryFlowCount',
        method: 'get',
        params: query
    })
}

// 文件类型占比个数
export function queryClassTypeFileCount(query) {
    return request({ 
        url: '/process/index/queryClassTypeFileCount',
        method: 'get',
        params: query
    })
}

// 文件类型下的子分类占比个数
export function queryFileCountByClassType(classType) {
    return request({ 
        url: '/process/index/queryFileCountByClassType?classType='+classType,
        method: 'get',
        params: {}
    })
}

// 二级部门归口文件个数
export function querySecDeptGkFileCount(query) {
    return request({ 
        url: '/process/index/querySecDeptGkFileCount',
        method: 'get',
        params: query
    })
}

// 二级部门阅知文件次数
export function querySecDeptViewCount(query) {
    return request({ 
        url: '/process/index/querySecDeptViewCount',
        method: 'get',
        params: query
    })
}


