import request from '@/utils/request'

// 查询编号规则明细列表
export function listCodeRuleDetail(query) {
  return request({
    url: '/setting/codeRuleDetail/list',
    method: 'get',
    params: query
  })
}

// 查询编号规则明细详细
export function getCodeRuleDetail(id) {
  return request({
    url: '/setting/codeRuleDetail/' + id,
    method: 'get'
  })
}

// 新增编号规则明细
export function addCodeRuleDetail(data) {
  return request({
    url: '/setting/codeRuleDetail',
    method: 'post',
    data: data
  })
}

// 修改编号规则明细
export function updateCodeRuleDetail(data) {
  return request({
    url: '/setting/codeRuleDetail',
    method: 'put',
    data: data
  })
}

// 删除编号规则明细
export function delCodeRuleDetail(id) {
  return request({
    url: '/setting/codeRuleDetail/' + id,
    method: 'delete'
  })
}
