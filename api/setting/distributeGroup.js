import request from '@/utils/request'

// 查询预设分组人员列表
export function listDistributeGroup(query) {
  return request({
    url: '/setting/distributeGroup/list',
    method: 'get',
    params: query
  })
}

export function distributeGroupByDocClass(docClass) {
  return request({
    url: '/setting/distributeGroup/list/'+docClass,
    method: 'get',
  })
}

// 查询预设分组人员详细
export function getDistributeGroup(id) {
  return request({
    url: '/setting/distributeGroup/' + id,
    method: 'get'
  })
}

// 新增预设分组人员
export function addDistributeGroup(data) {
  return request({
    url: '/setting/distributeGroup',
    method: 'post',
    data: data
  })
}

// 修改预设分组人员
export function updateDistributeGroup(data) {
  return request({
    url: '/setting/distributeGroup',
    method: 'put',
    data: data
  })
}

// 删除预设分组人员
export function delDistributeGroup(id) {
  return request({
    url: '/setting/distributeGroup/' + id,
    method: 'delete'
  })
}
