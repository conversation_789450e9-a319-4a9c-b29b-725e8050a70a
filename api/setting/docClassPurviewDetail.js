import request from '@/utils/request'

// 查询文件类型权限详情列表
export function listDocClassPurviewDetail(query) {
  return request({
    url: '/setting/docClassPurviewDetail/list',
    method: 'get',
    params: query
  })
}

// 查询文件类型权限详情详细
export function getDocClassPurviewDetail(id) {
  return request({
    url: '/setting/docClassPurviewDetail/' + id,
    method: 'get'
  })
}

// 新增文件类型权限详情
export function addDocClassPurviewDetail(data) {
  return request({
    url: '/setting/docClassPurviewDetail',
    method: 'post',
    data: data
  })
}

// 修改文件类型权限详情
export function updateDocClassPurviewDetail(data) {
  return request({
    url: '/setting/docClassPurviewDetail',
    method: 'put',
    data: data
  })
}

// 删除文件类型权限详情
export function delDocClassPurviewDetail(id) {
  return request({
    url: '/setting/docClassPurviewDetail/' + id,
    method: 'delete'
  })
}
