import request from '@/utils/request'

// 查询编号规则列表
export function listCodeRule(query) {
  return request({
    url: '/setting/codeRule/list',
    method: 'get',
    params: query
  })
}

// 查询编号规则详细
export function getCodeRule(id) {
  return request({
    url: '/setting/codeRule/' + id,
    method: 'get'
  })
}

// 新增编号规则
export function addCodeRule(data) {
  return request({
    url: '/setting/codeRule',
    method: 'post',
    data: data
  })
}
// 人工生成编码
export function CreateNewNo(data) {
  return request({
    url: '/setting/codeRule/CreateNewNo',
    method: 'post',
    data: data
  })
}
// 人工生成编码
export function createNewNo4Record(data) {
  return request({
    url: '/setting/codeRule/createNewNo4Record',
    method: 'post',
    data: data
  })
}
// 人工生成编码
export function codeRuleserialnumber(data) {
  return request({
    url: '/setting/codeRule/serialnumber',
    method: 'post',
    data: data
  })
}
// 人工生成编码
export function docSerialnumber(data) {
  return request({
    url: '/process/modifyApply/doc_serialnumber',
    method: 'post',
    data: data
  })
}
// 人工生成编码
export function recordSerialnumber(data) {
  return request({
    url: '/process/modifyApply/record_serialnumber',
    method: 'post',
    data: data
  })
}

// 修改编号规则
export function updateCodeRule(data) {
  return request({
    url: '/setting/codeRule',
    method: 'put',
    data: data
  })
}

// 删除编号规则
export function delCodeRule(id) {
  return request({
    url: '/setting/codeRule/' + id,
    method: 'delete'
  })
}
