import request from '@/utils/request'

// 查询版本规则明细列表
export function listVersionRuleDetail(query) {
  return request({
    url: '/setting/versionRuleDetail/list',
    method: 'get',
    params: query
  })
}

// 查询版本规则明细详细
export function getVersionRuleDetail(id) {
  return request({
    url: '/setting/versionRuleDetail/' + id,
    method: 'get'
  })
}

// 新增版本规则明细
export function addVersionRuleDetail(data) {
  return request({
    url: '/setting/versionRuleDetail',
    method: 'post',
    data: data
  })
}

// 修改版本规则明细
export function updateVersionRuleDetail(data) {
  return request({
    url: '/setting/versionRuleDetail',
    method: 'put',
    data: data
  })
}

// 删除版本规则明细
export function delVersionRuleDetail(id) {
  return request({
    url: '/setting/versionRuleDetail/' + id,
    method: 'delete'
  })
}

export function getNextVersion(query) {
  return request({
    url: '/setting/versionRuleDetail/next/version',
    method: 'post',
    params: query
  })
}
