import request from '@/utils/request'

// 查询版本规则列表
export function listVersionRule(query) {
  return request({
    url: '/setting/versionRule/list',
    method: 'get',
    params: query
  })
}

// 查询版本规则详细
export function getVersionRule(id) {
  return request({
    url: '/setting/versionRule/' + id,
    method: 'get'
  })
}

// 新增版本规则
export function addVersionRule(data) {
  return request({
    url: '/setting/versionRule',
    method: 'post',
    data: data
  })
}

// 修改版本规则
export function updateVersionRule(data) {
  return request({
    url: '/setting/versionRule',
    method: 'put',
    data: data
  })
}

// 删除版本规则
export function delVersionRule(id) {
  return request({
    url: '/setting/versionRule/' + id,
    method: 'delete'
  })
}
