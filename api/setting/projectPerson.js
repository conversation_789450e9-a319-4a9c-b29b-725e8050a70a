import request from '@/utils/request'

// 查询项目人员配置列表
export function listProjectPerson(query) {
  return request({
    url: '/setting/projectPerson/list',
    method: 'get',
    params: query
  })
}

// 导出项目人员配置列表
export function exportProjectPerson(query) {
  return request({
    url: '/setting/projectPerson/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获取项目人员配置详细信息
export function getProjectPerson(id) {
  return request({
    url: '/setting/projectPerson/' + id,
    method: 'get'
  })
}

// 新增项目人员配置
export function addProjectPerson(data) {
  return request({
    url: '/setting/projectPerson/add',
    method: 'post',
    data: data
  })
}

// 修改项目人员配置
export function updateProjectPerson(data) {
  return request({
    url: '/setting/projectPerson/update',
    method: 'post',
    data: data
  })
}

// 删除项目人员配置
export function removeProjectPerson(ids) {
  return request({
    url: '/setting/projectPerson/' + ids,
    method: 'delete'
  })
}

// 根据项目编码查询人员配置
export function getProjectPersonByCode(projectCode) {
  return request({
    url: '/setting/projectPerson/getByProjectCode/' + projectCode,
    method: 'get'
  })
}

// 查询所有项目人员配置列表
export function listAllProjectPerson(query) {
  return request({
    url: '/setting/projectPerson/listAll',
    method: 'get',
    params: query
  })
}
