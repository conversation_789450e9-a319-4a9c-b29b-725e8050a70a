import request from '@/utils/request'

// 查询预制编号列表
export function listPrepareId(query) {
  return request({
    url: '/setting/prepareId/list',
    method: 'get',
    params: query
  })
}

// 查询预制编号详细
export function getPrepareId(docId) {
  return request({
    url: '/setting/prepareId/' + docId,
    method: 'get'
  })
}

// 新增预制编号
export function addPrepareId(data) {
  return request({
    url: '/setting/prepareId',
    method: 'post',
    data: data
  })
}

// 修改预制编号
export function updatePrepareId(data) {
  return request({
    url: '/setting/prepareId',
    method: 'put',
    data: data
  })
}

// 删除预制编号
export function delPrepareId(docId) {
  return request({
    url: '/setting/prepareId/' + docId,
    method: 'delete'
  })
}
