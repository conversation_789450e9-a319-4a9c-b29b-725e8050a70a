import request from '@/utils/request'

// 查询预设分组人员详情列表
export function listDistributeGroupDetail(query) {
  return request({
    url: '/setting/distributeGroupDetail/list',
    method: 'get',
    params: query
  })
}

// 查询预设分组人员详情详细
export function getDistributeGroupDetail(id) {
  return request({
    url: '/setting/distributeGroupDetail/' + id,
    method: 'get'
  })
}

// 新增预设分组人员详情
export function addDistributeGroupDetail(data) {
  return request({
    url: '/setting/distributeGroupDetail',
    method: 'post',
    data: data
  })
}

// 修改预设分组人员详情
export function updateDistributeGroupDetail(data) {
  return request({
    url: '/setting/distributeGroupDetail',
    method: 'put',
    data: data
  })
}

// 删除预设分组人员详情
export function delDistributeGroupDetail(id) {
  return request({
    url: '/setting/distributeGroupDetail/' + id,
    method: 'delete'
  })
}
