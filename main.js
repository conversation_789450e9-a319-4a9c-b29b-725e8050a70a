import Vue from 'vue'
import App from './App'
import store from './store' // store
import plugins from './plugins' // plugins
import DictData from '@/components/dict/init'
import DictTag from '@/components/dict/DictTag'
//import './permission' // permission
import {
	parseTime,
	resetForm,
	addDateRange,
	selectDictLabel,
	selectDictLabels,
	handleTree,
	deepTraversal,
	isEmpty,
	getChildrenList,
	getUrlParams,
	dictLanguage
} from "@/utils/ruoyi";
import i18n from './utils/i18n.js'


import uView from '@/uni_modules/uview-ui'
import {
	getConfigKey
} from './api/file_processing/modifiyApply'
Vue.use(uView)
Vue.use(plugins)
Vue.component('DictTag', DictTag)
Vue.config.productionTip = false
Vue.prototype.$store = store
Vue.prototype.parseTime = parseTime
Vue.prototype.isEmpty = isEmpty
Vue.prototype.resetForm = resetForm
Vue.prototype.addDateRange = addDateRange
Vue.prototype.selectDictLabel = selectDictLabel
Vue.prototype.selectDictLabels = selectDictLabels
Vue.prototype.handleTree = handleTree
Vue.prototype.dictLanguage = dictLanguage
Vue.prototype.deepTraversal = deepTraversal
Vue.prototype.getChildrenList = getChildrenList
Vue.prototype.getUrlParams = getUrlParams
Vue.prototype.getConfigKey = getConfigKey
DictData.install()
App.mpType = 'app'

const app = new Vue({
	...App,
	i18n
})

app.$mount()