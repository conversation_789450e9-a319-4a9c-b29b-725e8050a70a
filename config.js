import envConfig from '@/env'
// #ifndef APP
const baseUrl = envConfig[process.env.ENV_TYPE].VUE_APP_BASE_HOST
const inetUrl = envConfig[process.env.ENV_TYPE].VUE_APP_INET
// #endif
// 应用全局配置
module.exports = {
    //baseUrl: 'http://*************:8080/dev-dms-admin',
	baseUrl: baseUrl,
	inetUrl: inetUrl,
	defaultLanguge: 'zh',
	// baseUrl: 'https://office.rzdata.net/dms-admin',
	// 代办处理是否显示返回按钮
	showBack:false,
	// 应用信息
	appInfo: {
		// 应用名称
		name: "体系文件管理系统",
		// 应用版本
		version: "1.1.0",
		// 应用logo
		logo: "/static/logo.png",
		// 官方网站
		site_url: "http://ruoyi.vip",
		// 政策协议
		agreements: [{
				title: "隐私政策",
				url: "https://ruoyi.vip/protocol.html"
			},
			{
				title: "用户服务协议",
				url: "https://ruoyi.vip/protocol.html"
			}
		]
	}
}