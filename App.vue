<script>
	import config from './config'
	import store from '@/store'
	import {
		getToken
	} from '@/utils/auth'

	import invoke from './permission.js'
	export default {
		onLaunch: function({path}) {
			this.initApp(path)
			this.initLanguage()
		},
		methods: {
			setTabBarItem() {
				uni.setTabBarItem({
					index: 0,
					text: this.$t("Home")
				})
				uni.setTabBarItem({
					index: 1,
					text: this.$t("Mine")
				})
				uni.setTabBarItem({
					index: 2,
					text: this.$t("Document")
				})
				uni.setTabBarItem({
					index: 3,
					text: this.$t("Person Center")
				}).then((de) => {
					console.log("dddddddddd", de)
				})
			},
			async initLanguage() {
				let language = uni.getStorageSync('language') || config.defaultLanguge
				// 设置语言类型
				this.$i18n.locale = language

				// 请求对应语言
				let message = {}
				if (!uni.getStorageSync('languageData')) {
					uni.request({
						url: `${config.inetUrl}/international/findLangPackage?type=front`,
						method: 'get',
						header: {
							"Accept-Language": language,
							'tenantid': 'CAM'
						},
					}).then((res) => {
						console.log(res);
						if (res && res[1].data.resultCode != 200) {
							return
						}
						const {
							result
						} = res[1].data
						this.$nextTick(() => {
						uni.setStorageSync('languageData', JSON.stringify(result))
						uni.setStorageSync('language', language)
						this.$i18n.setLocaleMessage(language, result)
							this.setTabBarItem()

						})
					})


				} else {
					message = JSON.parse(uni.getStorageSync('languageData'))
					this.$i18n.setLocaleMessage(language, message)
				}
			},

			// 初始化应用
			initApp(path) {
				// 初始化应用配置
				this.initConfig()
				// 检查用户登录状态
				//#ifdef H5
				 this.checkLogin(path)
				//#endif
				// 禁用双指放大
				document.documentElement.addEventListener('touchstart', function(event) {
					if (event.touches.length > 1) {
						event.preventDefault();
					}
				}, {
					passive: false
				});

				// 禁用双击放大
				var lastTouchEnd = 0;
				document.documentElement.addEventListener('touchend', function(event) {
					var now = Date.now();
					if (now - lastTouchEnd <= 300) {
						event.preventDefault();
					}
					lastTouchEnd = now;
				}, {
					passive: false
				});
			},
			initConfig() {
				this.globalData.config = config
			},
			checkLogin(path) {
				let currentRoute = `/${path}`;
				invoke({url:currentRoute})
			}
		}
	}
</script>

<style lang="scss">
	@import "@/uni_modules/uview-ui/index.scss";
	@import '@/static/scss/index.scss'
</style>