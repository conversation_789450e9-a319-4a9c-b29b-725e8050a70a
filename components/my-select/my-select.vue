<template>
	<view>
		<u--input :value="label" :disabled="disabled" disabledColor="#ffffff" @focus="show = true" :placeholder="placeholder" :border="disabled?'none':'bottom'">
		</u--input>
		
		<u-action-sheet :show="show" :actions="actions" :title="placeholder" 
			@close="show = false" @select="select">
		</u-action-sheet>
	</view>
</template>

<script>
	export default {
		name: "my-select",
		props: {
			data: {
				type: Array
			},
			disabled: {
				type: Boolean,
				required: false,
				default: false
			},
			value: {
				required: false
			},
			placeholder: {
				required: false
			}

		},
		computed: {
			label() {
				let item= this.data.find(v=>v.value==this.value)
				return item?item.label:'';
			},
			actions(){
				return this.data?this.data.map(v=>{return {name:v.label};}):[]
			}
		},
		data() {
			return {
				show:false,
			};
		},
		methods:{
			select(item){
				item= this.data.find(v=>v.label==item.name)
				this.$emit('input',item.value)
				this.$emit('onselected',item)
			}
		}
	}
</script>

<style>

</style>