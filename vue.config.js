const envConfig=require('./env.js')
let env =process.env.UNI_SCRIPT
let config = envConfig[env]
const { codeInspectorPlugin } = require('code-inspector-plugin');

let proxy = {}
proxy[config.VUE_APP_BASE_API] = {
	target: config.VUE_APP_BASE_HOST,
	changeOrigin: true,
	pathRewrite: {}
}
proxy[config.VUE_APP_INET] = {
	target: config.VUE_APP_INET_API,
	changeOrigin: true,
	pathRewrite: {     ['^' + process.env.VUE_APP_INET]: ''}
}
for (let item in proxy) {
	proxy[item].pathRewrite['^'+item] = ''
}
module.exports = {
	"devServer": {
		"port": 8081, //浏览器运行端口
		"https": false,
		"proxy": proxy
	},
    chainWebpack: (config) => {
        config.plugin('code-inspector-plugin').use(
            codeInspectorPlugin({
                bundler: 'webpack',
				editor: 'webstorm',
            })
        );
    }
}